{"version": 3, "sources": ["../src/lib/configs.ts", "../src/lib/Result.ts", "../src/validators/util/getValue.ts", "../src/constraints/ObjectConstrains.ts", "../src/lib/errors/ExpectedConstraintError.ts", "../src/lib/errors/BaseError.ts", "../src/lib/errors/BaseConstraintError.ts", "../src/validators/BaseValidator.ts", "../src/constraints/util/isUnique.ts", "../src/constraints/util/operators.ts", "../src/constraints/ArrayConstraints.ts", "../src/lib/errors/CombinedPropertyError.ts", "../src/lib/errors/ValidationError.ts", "../src/validators/ArrayValidator.ts", "../src/constraints/BigIntConstraints.ts", "../src/validators/BigIntValidator.ts", "../src/constraints/BooleanConstraints.ts", "../src/validators/BooleanValidator.ts", "../src/constraints/DateConstraints.ts", "../src/validators/DateValidator.ts", "../src/lib/errors/ExpectedValidationError.ts", "../src/validators/InstanceValidator.ts", "../src/validators/LiteralValidator.ts", "../src/validators/NeverValidator.ts", "../src/validators/NullishValidator.ts", "../src/constraints/NumberConstraints.ts", "../src/validators/NumberValidator.ts", "../src/lib/errors/MissingPropertyError.ts", "../src/lib/errors/UnknownPropertyError.ts", "../src/validators/DefaultValidator.ts", "../src/lib/errors/CombinedError.ts", "../src/validators/UnionValidator.ts", "../src/validators/ObjectValidator.ts", "../src/validators/PassthroughValidator.ts", "../src/validators/RecordValidator.ts", "../src/validators/SetValidator.ts", "../src/constraints/util/emailValidator.ts", "../src/constraints/util/net.ts", "../src/constraints/util/phoneValidator.ts", "../src/lib/errors/MultiplePossibilitiesConstraintError.ts", "../src/constraints/util/common/combinedResultFn.ts", "../src/constraints/util/urlValidators.ts", "../src/constraints/StringConstraints.ts", "../src/validators/StringValidator.ts", "../src/validators/TupleValidator.ts", "../src/validators/MapValidator.ts", "../src/validators/LazyValidator.ts", "../src/lib/errors/UnknownEnumValueError.ts", "../src/validators/NativeEnumValidator.ts", "../src/constraints/TypedArrayLengthConstraints.ts", "../src/constraints/util/common/vowels.ts", "../src/constraints/util/typedArray.ts", "../src/validators/TypedArrayValidator.ts", "../src/lib/Shapes.ts", "../src/index.ts"], "names": ["uniqueArray", "inspect", "value", "s"], "mappings": ";;;;AAAA,IAAI,oBAAoB;AAMjB,SAAS,2BAA2B,SAAkB;AAC5D,sBAAoB;AACrB;AAFgB;AAOT,SAAS,6BAA6B;AAC5C,SAAO;AACR;AAFgB;;;ACbT,IAAM,UAAN,MAAM,QAAmC;AAAA,EAKvC,YAAY,SAAkB,OAAW,OAAW;AAC3D,SAAK,UAAU;AACf,QAAI,SAAS;AACZ,WAAK,QAAQ;AAAA,IACd,OAAO;AACN,WAAK,QAAQ;AAAA,IACd;AAAA,EACD;AAAA,EAEO,OAA4C;AAClD,WAAO,KAAK;AAAA,EACb;AAAA,EAEO,QAA8C;AACpD,WAAO,CAAC,KAAK;AAAA,EACd;AAAA,EAEO,SAAY;AAClB,QAAI,KAAK,KAAK;AAAG,aAAO,KAAK;AAC7B,UAAM,KAAK;AAAA,EACZ;AAAA,EAEA,OAAc,GAA+B,OAAwB;AACpE,WAAO,IAAI,QAAa,MAAM,KAAK;AAAA,EACpC;AAAA,EAEA,OAAc,IAAgC,OAAwB;AACrE,WAAO,IAAI,QAAa,OAAO,QAAW,KAAK;AAAA,EAChD;AACD;AAlCgD;AAAzC,IAAM,SAAN;;;ACGA,SAAS,SAAkD,WAAiB;AAClF,SAAO,OAAO,cAAc,aAAa,UAAU,IAAI;AACxD;AAFgB;;;ACHhB,OAAO,SAAS;;;ACAhB,SAAS,eAA4C;;;ACE9C,IAAM,sBAAsB,OAAO,IAAI,4BAA4B;AACnE,IAAM,+BAA+B,OAAO,IAAI,uCAAuC;AAEvF,IAAe,aAAf,MAAe,mBAAkB,MAAM;AAAA,EAC7C,CAAW,mBAAmB,EAAE,OAAe,SAAiC;AAC/E,WAAO,GAAG,KAAK,4BAA4B,EAAE,OAAO,OAAO,CAAC;AAAA,EAAK,KAAK,MAAO,MAAM,KAAK,MAAO,QAAQ,IAAI,CAAC,CAAC;AAAA,EAC9G;AAGD;AAN8C;AAAvC,IAAe,YAAf;;;ACiBA,IAAe,uBAAf,MAAe,6BAAyC,UAAU;AAAA,EAIjE,YAAY,YAAkC,SAAiB,OAAU;AAC/E,UAAM,OAAO;AACb,SAAK,aAAa;AAClB,SAAK,QAAQ;AAAA,EACd;AACD;AATyE;AAAlE,IAAe,sBAAf;;;AFlBA,IAAM,2BAAN,MAAM,iCAA6C,oBAAuB;AAAA,EAGzE,YAAY,YAAkC,SAAiB,OAAU,UAAkB;AACjG,UAAM,YAAY,SAAS,KAAK;AAChC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEO,SAAS;AACf,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,aAAa,QAAQ,QAAQ,KAAK,YAAY,QAAQ;AAC5D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,6BAA6B,UAAU,KAAK,SAAS;AAAA,IAC7E;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,EAAE;AAE3F,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQ,QAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,2BAA2B,SAAS,CAAC,MAAM,UAAU;AACvF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,gBAAgB;AAAA,IAAO,QAAQ,QAAQ,cAAc,QAAQ,CAAC,GAAG,QAAQ,QAAQ,KAAK,UAAU,SAAS,CAAC;AAChH,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,aAAa;AAAA,EAAK,UAAU;AAAA,EAChE;AACD;AAlCiF;AAA1E,IAAM,0BAAN;;;ADYA,SAAS,eACf,KACA,SACA,WACiB;AACjB,SAAO;AAAA,IACN,IAAI,OAAU,QAAc;AAC3B,UAAI,CAAC,QAAQ;AACZ,eAAO,OAAO,IAAI,IAAI,wBAAwB,oBAAoB,2BAA2B,QAAQ,4BAA4B,CAAC;AAAA,MACnI;AAEA,YAAM,aAAa,MAAM,QAAQ,GAAG;AAEpC,YAAM,QAAQ,aAAa,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,IAAI,IAAI,QAAQ,GAAG;AAE3E,YAAM,YAAY,iBAAyB,SAAS,OAAO,UAAU,IAAI,QAAQ,OAAO,QAAQ;AAEhG,UAAI,WAAW;AACd,eAAO,UAAU,SAAS,EAAE,IAAI,KAAK;AAAA,MACtC;AAEA,aAAO,OAAO,GAAG,KAAK;AAAA,IACvB;AAAA,EACD;AACD;AAxBgB;AA0BhB,SAAS,iBAAoE,SAA8B,OAAY,YAAqB;AAC3I,MAAI,QAAQ,OAAO,QAAW;AAC7B,WAAO,aAAa,CAAC,MAAM,KAAK,CAAC,QAAa,CAAC,GAAG,IAAI,QAAQ,KAAK;AAAA,EACpE;AAEA,MAAI,OAAO,QAAQ,OAAO,YAAY;AACrC,WAAO,QAAQ,GAAG,KAAK;AAAA,EACxB;AAEA,SAAO,UAAU,QAAQ;AAC1B;AAVS;;;AI7BF,IAAe,iBAAf,MAAe,eAAiB;AAAA,EAM/B,YAAY,cAAyC,CAAC,GAAG;AAHhE,SAAU,cAAyC,CAAC;AACpD,SAAU,sBAAwD;AAGjE,SAAK,cAAc;AAAA,EACpB;AAAA,EAEO,UAAU,QAAsB;AACtC,SAAK,SAAS;AACd,WAAO;AAAA,EACR;AAAA,EAEA,IAAW,WAA0C;AACpD,WAAO,IAAI,eAAe,CAAC,IAAI,iBAAiB,MAAS,GAAG,KAAK,MAAM,CAAC,CAAC;AAAA,EAC1E;AAAA,EAEA,IAAW,WAAqC;AAC/C,WAAO,IAAI,eAAe,CAAC,IAAI,iBAAiB,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC;AAAA,EACrE;AAAA,EAEA,IAAW,UAAgD;AAC1D,WAAO,IAAI,eAAe,CAAC,IAAI,iBAAiB,GAAG,KAAK,MAAM,CAAC,CAAC;AAAA,EACjE;AAAA,EAEA,IAAW,QAA6B;AACvC,WAAO,IAAI,eAAoB,KAAK,MAAM,CAAC;AAAA,EAC5C;AAAA,EAEA,IAAW,MAAuB;AACjC,WAAO,IAAI,aAAgB,KAAK,MAAM,CAAC;AAAA,EACxC;AAAA,EAEO,MAAS,YAAgE;AAC/E,WAAO,IAAI,eAAsB,CAAC,KAAK,MAAM,GAAG,GAAG,UAAU,CAAC;AAAA,EAC/D;AAAA,EAIO,UAAa,IAAuC;AAC1D,WAAO,KAAK,cAAc,EAAE,KAAK,CAAC,UAAU,OAAO,GAAG,GAAG,KAAK,CAAiB,EAAE,CAAC;AAAA,EACnF;AAAA,EAIO,QAA2D,IAAuC;AACxG,WAAO,KAAK,cAAc,EAAE,KAAK,GAAiE,CAAC;AAAA,EACpG;AAAA,EAEO,QAAQ,OAAuG;AACrH,WAAO,IAAI,iBAAiB,KAAK,MAAM,GAAsD,KAAK;AAAA,EACnG;AAAA,EAEO,KAAkE,KAAU,SAAuC;AACzH,WAAO,KAAK,cAAc,eAA6B,KAAK,SAAS,IAAuB,CAAC;AAAA,EAC9F;AAAA,EAEO,SAAS,aAA2B;AAC1C,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,cAAc;AACpB,WAAO;AAAA,EACR;AAAA,EAEO,IAAI,OAAsC;AAChD,QAAI,SAAS,KAAK,OAAO,KAAK;AAC9B,QAAI,OAAO,MAAM;AAAG,aAAO;AAE3B,eAAW,cAAc,KAAK,aAAa;AAC1C,eAAS,WAAW,IAAI,OAAO,OAAY,KAAK,MAAM;AACtD,UAAI,OAAO,MAAM;AAAG;AAAA,IACrB;AAEA,WAAO;AAAA,EACR;AAAA,EAEO,MAAuB,OAAmB;AAGhD,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,KAAK,OAAO,KAAK,EAAE,OAAO;AAAA,IAClC;AAEA,WAAO,KAAK,YAAY,OAAO,CAAC,GAAG,eAAe,WAAW,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,OAAO,KAAK,EAAE,OAAO,CAAC;AAAA,EAC1G;AAAA,EAEO,GAAoB,OAA4B;AACtD,WAAO,KAAK,IAAI,KAAK,EAAE,KAAK;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,qBAAqB,qBAA6D;AACxF,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,sBAAsB;AAC5B,WAAO;AAAA,EACR;AAAA,EAEO,uBAAuB;AAC7B,WAAO,SAAS,KAAK,mBAAmB;AAAA,EACzC;AAAA,EAEA,IAAc,uBAAgC;AAC7C,WAAO,SAAS,KAAK,mBAAmB,KAAK,2BAA2B;AAAA,EACzE;AAAA,EAEU,QAAc;AACvB,UAAM,QAAc,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC;AAC1E,UAAM,sBAAsB,KAAK;AACjC,WAAO;AAAA,EACR;AAAA,EAIU,cAAc,YAAkC;AACzD,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,cAAc,MAAM,YAAY,OAAO,UAAU;AACvD,WAAO;AAAA,EACR;AACD;AA3HuC;AAAhC,IAAe,gBAAf;;;ACbP,OAAO,mBAAmB;AAC1B,OAAO,cAAc;AAEd,SAAS,SAAS,OAAkB;AAC1C,MAAI,MAAM,SAAS;AAAG,WAAO;AAC7B,QAAMA,eAAc,SAAS,OAAO,aAAa;AACjD,SAAOA,aAAY,WAAW,MAAM;AACrC;AAJgB;;;ACDT,SAAS,SAAS,GAAoB,GAA6B;AACzE,SAAO,IAAI;AACZ;AAFgB;AAMT,SAAS,gBAAgB,GAAoB,GAA6B;AAChF,SAAO,KAAK;AACb;AAFgB;AAMT,SAAS,YAAY,GAAoB,GAA6B;AAC5E,SAAO,IAAI;AACZ;AAFgB;AAMT,SAAS,mBAAmB,GAAoB,GAA6B;AACnF,SAAO,KAAK;AACb;AAFgB;AAMT,SAAS,MAAM,GAAoB,GAA6B;AACtE,SAAO,MAAM;AACd;AAFgB;AAMT,SAAS,SAAS,GAAoB,GAA6B;AACzE,SAAO,MAAM;AACd;AAFgB;;;ACbhB,SAAS,sBAAyB,YAAwB,MAA2B,UAAkB,QAAkC;AACxI,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,WAAW,MAAM,QAAQ,MAAM,IACnC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IACzF;AAAA,EACD;AACD;AARS;AAUF,SAAS,oBAAuB,OAAiC;AACvE,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,sBAAsB,UAAU,6BAA6B,UAAU,KAAK;AACpF;AAHgB;AAKT,SAAS,2BAA8B,OAAiC;AAC9E,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,sBAAsB,iBAAiB,oCAAoC,UAAU,KAAK;AAClG;AAHgB;AAKT,SAAS,uBAA0B,OAAiC;AAC1E,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,sBAAsB,aAAa,gCAAgC,UAAU,KAAK;AAC1F;AAHgB;AAKT,SAAS,8BAAiC,OAAiC;AACjF,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,sBAAsB,oBAAoB,uCAAuC,UAAU,KAAK;AACxG;AAHgB;AAKT,SAAS,iBAAoB,OAAiC;AACpE,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,sBAAsB,OAAO,0BAA0B,UAAU,KAAK;AAC9E;AAHgB;AAKT,SAAS,oBAAuB,OAAiC;AACvE,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,sBAAsB,UAAU,6BAA6B,UAAU,KAAK;AACpF;AAHgB;AAKT,SAAS,iBAAoB,OAAe,WAAqC;AACvF,QAAM,WAAW,sBAAsB,KAAK,yBAAyB,SAAS;AAC9E,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,MAAM,UAAU,SAAS,MAAM,SAAS,YAC5C,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,0BAA0B,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IAC7G;AAAA,EACD;AACD;AATgB;AAWT,SAAS,0BAA6B,OAAe,KAA+B;AAC1F,QAAM,WAAW,sBAAsB,KAAK,0BAA0B,GAAG;AACzE,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,MAAM,UAAU,SAAS,MAAM,UAAU,MAC7C,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,mCAAmC,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IACtH;AAAA,EACD;AACD;AATgB;AAWT,SAAS,0BAA6B,YAAoB,WAAqC;AACrG,QAAM,WAAW,qBAAqB,UAAU,yBAAyB,SAAS;AAClF,SAAO;AAAA,IACN,IAAI,OAAY;AACf,aAAO,MAAM,SAAS,cAAc,MAAM,SAAS,YAChD,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,mCAAmC,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IACtH;AAAA,EACD;AACD;AATgB;AAWT,IAAM,cAAsC;AAAA,EAClD,IAAI,OAAkB;AACrB,WAAO,SAAS,KAAK,IAClB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,qBAAqB,+BAA+B,OAAO,kCAAkC,CAAC;AAAA,EACzI;AACD;;;AC/FO,IAAM,yBAAN,MAAM,+BAA8B,UAAU;AAAA,EAG7C,YAAY,QAAoC;AACtD,UAAM,6BAA6B;AAEnC,SAAK,SAAS;AAAA,EACf;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,2BAA2B,SAAS;AAAA,IAC5D;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AAExD,UAAM,SAAS,GAAG,QAAQ,QAAQ,yBAAyB,SAAS,CAAC,KAAK,QAAQ,QAAQ,KAAK,OAAO,OAAO,SAAS,GAAG,QAAQ,CAAC;AAClI,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,SAAS,KAAK,OAClB,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AACtB,YAAM,WAAW,uBAAsB,eAAe,KAAK,OAAO;AAClE,YAAM,OAAO,MAAM,4BAA4B,EAAE,QAAQ,GAAG,UAAU,EAAE,QAAQ,OAAO,OAAO;AAE9F,aAAO,UAAU,QAAQ,GAAG,OAAO,GAAG,IAAI;AAAA,IAC3C,CAAC,EACA,KAAK,MAAM;AACb,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA;AAAA,EAAO,MAAM;AAAA,EAC5C;AAAA,EAEA,OAAe,eAAe,KAAkB,SAAyC;AACxF,QAAI,OAAO,QAAQ;AAAU,aAAO,QAAQ,QAAQ,IAAI,GAAG,IAAI,QAAQ;AACvE,QAAI,OAAO,QAAQ;AAAU,aAAO,IAAI,QAAQ,QAAQ,IAAI,SAAS,GAAG,QAAQ,CAAC;AACjF,WAAO,IAAI,QAAQ,QAAQ,UAAU,QAAQ,CAAC,IAAI,IAAI,WAAW;AAAA,EAClE;AACD;AApCqD;AAA9C,IAAM,wBAAN;;;ACHP,SAAS,WAAAC,gBAA4C;AAG9C,IAAM,mBAAN,MAAM,yBAAwB,UAAU;AAAA,EAIvC,YAAY,WAAmB,SAAiB,OAAgB;AACtE,UAAM,OAAO;AAEb,SAAK,YAAY;AACjB,SAAK,QAAQ;AAAA,EACd;AAAA,EAEO,SAAS;AACf,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,OAAO,KAAK;AAAA,IACb;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,YAAY,QAAQ,QAAQ,KAAK,WAAW,QAAQ;AAC1D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,qBAAqB,SAAS,KAAK,SAAS;AAAA,IACpE;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQA,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,mBAAmB,SAAS,CAAC,MAAM,SAAS;AAC9E,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,UAAU;AAAA,EAC9C;AACD;AAnC+C;AAAxC,IAAM,kBAAN;;;ACiBA,IAAM,kBAAN,MAAM,wBAA2D,cAAiB;AAAA,EAGjF,YAAY,WAA6B,cAAyC,CAAC,GAAG;AAC5F,UAAM,WAAW;AACjB,SAAK,YAAY;AAAA,EAClB;AAAA,EAEO,eAAiC,QAAgF;AACvH,WAAO,KAAK,cAAc,oBAAoB,MAAM,CAAmB;AAAA,EACxE;AAAA,EAEO,sBAAwC,QAAkE;AAChH,WAAO,KAAK,cAAc,2BAA2B,MAAM,CAAmB;AAAA,EAC/E;AAAA,EAEO,kBAAoC,QAAsD;AAChG,WAAO,KAAK,cAAc,uBAAuB,MAAM,CAAmB;AAAA,EAC3E;AAAA,EAEO,yBAA2C,QAAmD;AACpG,WAAO,KAAK,cAAc,8BAA8B,MAAM,CAAmB;AAAA,EAClF;AAAA,EAEO,YAA8B,QAA6C;AACjF,WAAO,KAAK,cAAc,iBAAiB,MAAM,CAAmB;AAAA,EACrE;AAAA,EAEO,eAAe,QAAwC;AAC7D,WAAO,KAAK,cAAc,oBAAoB,MAAM,CAAmB;AAAA,EACxE;AAAA,EAEO,YACN,OACA,WACoI;AACpI,WAAO,KAAK,cAAc,iBAAiB,OAAO,SAAS,CAAmB;AAAA,EAC/E;AAAA,EAEO,qBACN,SACA,OACsH;AACtH,WAAO,KAAK,cAAc,0BAA0B,SAAS,KAAK,CAAmB;AAAA,EACtF;AAAA,EAEO,qBACN,YACA,WACsH;AACtH,WAAO,KAAK,cAAc,0BAA0B,YAAY,SAAS,CAAmB;AAAA,EAC7F;AAAA,EAEA,IAAW,SAAe;AACzB,WAAO,KAAK,cAAc,WAA6B;AAAA,EACxD;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,WAAW,CAAC;AAAA,EAC9E;AAAA,EAEU,OAAO,QAAqE;AACrF,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC3B,aAAO,OAAO,IAAI,IAAI,gBAAgB,cAAc,qBAAqB,MAAM,CAAC;AAAA,IACjF;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,MAAW;AAAA,IAC7B;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAiB,CAAC;AAExB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,YAAM,SAAS,KAAK,UAAU,IAAI,OAAO,CAAC,CAAC;AAC3C,UAAI,OAAO,KAAK;AAAG,oBAAY,KAAK,OAAO,KAAK;AAAA;AAC3C,eAAO,KAAK,CAAC,GAAG,OAAO,KAAM,CAAC;AAAA,IACpC;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,EAChD;AACD;AAnFyF;AAAlF,IAAM,iBAAN;;;ACNP,SAAS,iBAAiB,YAAwB,MAA4B,UAAkB,QAAqC;AACpI,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,WAAW,OAAO,MAAM,IAC5B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IACzF;AAAA,EACD;AACD;AARS;AAUF,SAAS,eAAe,OAAoC;AAClE,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,UAAU,qBAAqB,UAAU,KAAK;AACvE;AAHgB;AAKT,SAAS,sBAAsB,OAAoC;AACzE,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,iBAAiB,4BAA4B,UAAU,KAAK;AACrF;AAHgB;AAKT,SAAS,kBAAkB,OAAoC;AACrE,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,aAAa,wBAAwB,UAAU,KAAK;AAC7E;AAHgB;AAKT,SAAS,yBAAyB,OAAoC;AAC5E,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,oBAAoB,+BAA+B,UAAU,KAAK;AAC3F;AAHgB;AAKT,SAAS,YAAY,OAAoC;AAC/D,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,OAAO,kBAAkB,UAAU,KAAK;AACjE;AAHgB;AAKT,SAAS,eAAe,OAAoC;AAClE,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,UAAU,qBAAqB,UAAU,KAAK;AACvE;AAHgB;AAKT,SAAS,kBAAkB,SAAsC;AACvE,QAAM,WAAW,cAAc,OAAO;AACtC,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,QAAQ,YAAY,KACxB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,wBAAwB,2BAA2B,OAAO,QAAQ,CAAC;AAAA,IAC9G;AAAA,EACD;AACD;AATgB;;;ACxCT,IAAM,mBAAN,MAAM,yBAA0C,cAAiB;AAAA,EAChE,SAAS,QAAsB;AACrC,WAAO,KAAK,cAAc,eAAe,MAAM,CAAmB;AAAA,EACnE;AAAA,EAEO,gBAAgB,QAAsB;AAC5C,WAAO,KAAK,cAAc,sBAAsB,MAAM,CAAmB;AAAA,EAC1E;AAAA,EAEO,YAAY,QAAsB;AACxC,WAAO,KAAK,cAAc,kBAAkB,MAAM,CAAmB;AAAA,EACtE;AAAA,EAEO,mBAAmB,QAAsB;AAC/C,WAAO,KAAK,cAAc,yBAAyB,MAAM,CAAmB;AAAA,EAC7E;AAAA,EAEO,MAAwB,QAA+B;AAC7D,WAAO,KAAK,cAAc,YAAY,MAAM,CAAmB;AAAA,EAChE;AAAA,EAEO,SAAS,QAAsB;AACrC,WAAO,KAAK,cAAc,eAAe,MAAM,CAAmB;AAAA,EACnE;AAAA,EAEA,IAAW,WAAiB;AAC3B,WAAO,KAAK,mBAAmB,EAAE;AAAA,EAClC;AAAA,EAEA,IAAW,WAAiB;AAC3B,WAAO,KAAK,SAAS,EAAE;AAAA,EACxB;AAAA,EAEO,YAAY,QAAsB;AACxC,WAAO,KAAK,cAAc,kBAAkB,MAAM,CAAmB;AAAA,EACtE;AAAA,EAEA,IAAW,MAAY;AACtB,WAAO,KAAK,UAAU,CAAC,UAAW,QAAQ,IAAI,CAAC,QAAQ,KAAW;AAAA,EACnE;AAAA,EAEO,KAAK,MAAoB;AAC/B,WAAO,KAAK,UAAU,CAAC,UAAU,OAAO,OAAO,MAAM,KAAK,CAAM;AAAA,EACjE;AAAA,EAEO,MAAM,MAAoB;AAChC,WAAO,KAAK,UAAU,CAAC,UAAU,OAAO,QAAQ,MAAM,KAAK,CAAM;AAAA,EAClE;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,WACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,YAAY,+BAA+B,KAAK,CAAC;AAAA,EACpF;AACD;AAtDwE;AAAjE,IAAM,kBAAN;;;ACRA,IAAM,cAA0C;AAAA,EACtD,IAAI,OAAgB;AACnB,WAAO,QACJ,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,kBAAkB,yBAAyB,OAAO,MAAM,CAAC;AAAA,EACpG;AACD;AAEO,IAAM,eAA4C;AAAA,EACxD,IAAI,OAAgB;AACnB,WAAO,QACJ,OAAO,IAAI,IAAI,wBAAwB,mBAAmB,yBAAyB,OAAO,OAAO,CAAC,IAClG,OAAO,GAAG,KAAK;AAAA,EACnB;AACD;;;ACdO,IAAM,oBAAN,MAAM,0BAAsD,cAAiB;AAAA,EACnF,IAAW,OAA+B;AACzC,WAAO,KAAK,cAAc,WAA6B;AAAA,EACxD;AAAA,EAEA,IAAW,QAAiC;AAC3C,WAAO,KAAK,cAAc,YAA8B;AAAA,EACzD;AAAA,EAEO,MAA8B,OAA+B;AACnE,WAAQ,QAAQ,KAAK,OAAO,KAAK;AAAA,EAClC;AAAA,EAEO,SAAiC,OAA+B;AACtE,WAAQ,QAAQ,KAAK,QAAQ,KAAK;AAAA,EACnC;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,YACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,aAAa,gCAAgC,KAAK,CAAC;AAAA,EACtF;AACD;AAtBoF;AAA7E,IAAM,mBAAN;;;ACSP,SAAS,eAAe,YAAwB,MAA0B,UAAkB,QAAmC;AAC9H,SAAO;AAAA,IACN,IAAI,OAAa;AAChB,aAAO,WAAW,MAAM,QAAQ,GAAG,MAAM,IACtC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,sBAAsB,OAAO,QAAQ,CAAC;AAAA,IACvF;AAAA,EACD;AACD;AARS;AAUF,SAAS,aAAa,OAAgC;AAC5D,QAAM,WAAW,cAAc,MAAM,YAAY,CAAC;AAClD,SAAO,eAAe,UAAU,mBAAmB,UAAU,MAAM,QAAQ,CAAC;AAC7E;AAHgB;AAKT,SAAS,oBAAoB,OAAgC;AACnE,QAAM,WAAW,eAAe,MAAM,YAAY,CAAC;AACnD,SAAO,eAAe,iBAAiB,0BAA0B,UAAU,MAAM,QAAQ,CAAC;AAC3F;AAHgB;AAKT,SAAS,gBAAgB,OAAgC;AAC/D,QAAM,WAAW,cAAc,MAAM,YAAY,CAAC;AAClD,SAAO,eAAe,aAAa,sBAAsB,UAAU,MAAM,QAAQ,CAAC;AACnF;AAHgB;AAKT,SAAS,uBAAuB,OAAgC;AACtE,QAAM,WAAW,eAAe,MAAM,YAAY,CAAC;AACnD,SAAO,eAAe,oBAAoB,6BAA6B,UAAU,MAAM,QAAQ,CAAC;AACjG;AAHgB;AAKT,SAAS,UAAU,OAAgC;AACzD,QAAM,WAAW,gBAAgB,MAAM,YAAY,CAAC;AACpD,SAAO,eAAe,OAAO,gBAAgB,UAAU,MAAM,QAAQ,CAAC;AACvE;AAHgB;AAKT,SAAS,aAAa,OAAgC;AAC5D,QAAM,WAAW,gBAAgB,MAAM,YAAY,CAAC;AACpD,SAAO,eAAe,UAAU,mBAAmB,UAAU,MAAM,QAAQ,CAAC;AAC7E;AAHgB;AAKT,IAAM,cAAiC;AAAA,EAC7C,IAAI,OAAa;AAChB,WAAO,OAAO,MAAM,MAAM,QAAQ,CAAC,IAChC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,kBAAkB,sBAAsB,OAAO,kBAAkB,CAAC;AAAA,EAC7G;AACD;AAEO,IAAM,YAA+B;AAAA,EAC3C,IAAI,OAAa;AAChB,WAAO,OAAO,MAAM,MAAM,QAAQ,CAAC,IAChC,OAAO,IAAI,IAAI,wBAAwB,gBAAgB,sBAAsB,OAAO,kBAAkB,CAAC,IACvG,OAAO,GAAG,KAAK;AAAA,EACnB;AACD;;;ACvDO,IAAM,iBAAN,MAAM,uBAAsB,cAAoB;AAAA,EAC/C,SAAS,MAAoC;AACnD,WAAO,KAAK,cAAc,aAAa,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,EACvD;AAAA,EAEO,gBAAgB,MAAoC;AAC1D,WAAO,KAAK,cAAc,oBAAoB,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,EAC9D;AAAA,EAEO,YAAY,MAAoC;AACtD,WAAO,KAAK,cAAc,gBAAgB,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,EAC1D;AAAA,EAEO,mBAAmB,MAAoC;AAC7D,WAAO,KAAK,cAAc,uBAAuB,IAAI,KAAK,IAAI,CAAC,CAAC;AAAA,EACjE;AAAA,EAEO,MAAM,MAAoC;AAChD,UAAM,WAAW,IAAI,KAAK,IAAI;AAC9B,WAAO,OAAO,MAAM,SAAS,QAAQ,CAAC,IACnC,KAAK,UACL,KAAK,cAAc,UAAU,QAAQ,CAAC;AAAA,EAC1C;AAAA,EAEO,SAAS,MAAoC;AACnD,UAAM,WAAW,IAAI,KAAK,IAAI;AAC9B,WAAO,OAAO,MAAM,SAAS,QAAQ,CAAC,IACnC,KAAK,QACL,KAAK,cAAc,aAAa,QAAQ,CAAC;AAAA,EAC7C;AAAA,EAEA,IAAW,QAAc;AACxB,WAAO,KAAK,cAAc,SAAS;AAAA,EACpC;AAAA,EAEA,IAAW,UAAgB;AAC1B,WAAO,KAAK,cAAc,WAAW;AAAA,EACtC;AAAA,EAEU,OAAO,OAA+C;AAC/D,WAAO,iBAAiB,OACrB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,gBAAgB,UAAU,mBAAmB,KAAK,CAAC;AAAA,EACtE;AACD;AA5CuD;AAAhD,IAAM,gBAAN;;;ACdP,SAAS,WAAAA,gBAA4C;AAI9C,IAAM,2BAAN,MAAM,iCAAmC,gBAAgB;AAAA,EAGxD,YAAY,WAAmB,SAAiB,OAAgB,UAAa;AACnF,UAAM,WAAW,SAAS,KAAK;AAC/B,SAAK,WAAW;AAAA,EACjB;AAAA,EAEgB,SAAS;AACxB,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,WAAW,KAAK;AAAA,MAChB,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,YAAY,QAAQ,QAAQ,KAAK,WAAW,QAAQ;AAC1D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,6BAA6B,SAAS,KAAK,SAAS;AAAA,IAC5E;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,EAAE;AAE3F,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,WAAWA,SAAQ,KAAK,UAAU,UAAU,EAAE,QAAQ,OAAO,OAAO;AAC1E,UAAM,QAAQA,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,2BAA2B,SAAS,CAAC,MAAM,SAAS;AACtF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,gBAAgB;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,QAAQ;AACxF,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,aAAa;AAAA,EAAK,UAAU;AAAA,EAChE;AACD;AAnCgE;AAAzD,IAAM,0BAAN;;;ACEA,IAAM,qBAAN,MAAM,2BAA6B,cAAiB;AAAA,EAGnD,YAAY,UAA0B,cAAyC,CAAC,GAAG;AACzF,UAAM,WAAW;AACjB,SAAK,WAAW;AAAA,EACjB;AAAA,EAEU,OAAO,OAAoE;AACpF,WAAO,iBAAiB,KAAK,WAC1B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,iBAAiB,YAAY,OAAO,KAAK,QAAQ,CAAC;AAAA,EAC7F;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EAC7E;AACD;AAjB2D;AAApD,IAAM,oBAAN;;;ACDA,IAAM,oBAAN,MAAM,0BAA4B,cAAiB;AAAA,EAGlD,YAAY,SAAY,cAAyC,CAAC,GAAG;AAC3E,UAAM,WAAW;AACjB,SAAK,WAAW;AAAA,EACjB;AAAA,EAEU,OAAO,OAAuD;AACvE,WAAO,OAAO,GAAG,OAAO,KAAK,QAAQ,IAClC,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,wBAAwB,gBAAgB,gCAAgC,OAAO,KAAK,QAAQ,CAAC;AAAA,EAChH;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EAC7E;AACD;AAjB0D;AAAnD,IAAM,mBAAN;;;ACDA,IAAM,kBAAN,MAAM,wBAAuB,cAAqB;AAAA,EAC9C,OAAO,OAAgD;AAChE,WAAO,OAAO,IAAI,IAAI,gBAAgB,WAAW,qCAAqC,KAAK,CAAC;AAAA,EAC7F;AACD;AAJyD;AAAlD,IAAM,iBAAN;;;ACAA,IAAM,oBAAN,MAAM,0BAAyB,cAAgC;AAAA,EAC3D,OAAO,OAA2D;AAC3E,WAAO,UAAU,UAAa,UAAU,OACrC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,gBAAgB,aAAa,8BAA8B,KAAK,CAAC;AAAA,EACpF;AACD;AANsE;AAA/D,IAAM,mBAAN;;;ACeP,SAAS,iBAAiB,YAAwB,MAA4B,UAAkB,QAAqC;AACpI,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,WAAW,OAAO,MAAM,IAC5B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,wBAAwB,OAAO,QAAQ,CAAC;AAAA,IACzF;AAAA,EACD;AACD;AARS;AAUF,SAAS,eAAe,OAAoC;AAClE,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,UAAU,qBAAqB,UAAU,KAAK;AACvE;AAHgB;AAKT,SAAS,sBAAsB,OAAoC;AACzE,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,iBAAiB,4BAA4B,UAAU,KAAK;AACrF;AAHgB;AAKT,SAAS,kBAAkB,OAAoC;AACrE,QAAM,WAAW,cAAc,KAAK;AACpC,SAAO,iBAAiB,aAAa,wBAAwB,UAAU,KAAK;AAC7E;AAHgB;AAKT,SAAS,yBAAyB,OAAoC;AAC5E,QAAM,WAAW,eAAe,KAAK;AACrC,SAAO,iBAAiB,oBAAoB,+BAA+B,UAAU,KAAK;AAC3F;AAHgB;AAKT,SAAS,YAAY,OAAoC;AAC/D,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,OAAO,kBAAkB,UAAU,KAAK;AACjE;AAHgB;AAKT,SAAS,eAAe,OAAoC;AAClE,QAAM,WAAW,gBAAgB,KAAK;AACtC,SAAO,iBAAiB,UAAU,qBAAqB,UAAU,KAAK;AACvE;AAHgB;AAKT,IAAM,YAAiC;AAAA,EAC7C,IAAI,OAAe;AAClB,WAAO,OAAO,UAAU,KAAK,IAC1B,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,MACP,IAAI,wBAAwB,gBAAgB,iCAAiC,OAAO,uCAAuC;AAAA,IAC3H;AAAA,EACJ;AACD;AAEO,IAAM,gBAAqC;AAAA,EACjD,IAAI,OAAe;AAClB,WAAO,OAAO,cAAc,KAAK,IAC9B,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,MACP,IAAI;AAAA,QACH;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACA;AAAA,EACJ;AACD;AAEO,IAAM,eAAoC;AAAA,EAChD,IAAI,OAAe;AAClB,WAAO,OAAO,SAAS,KAAK,IACzB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,mBAAmB,6BAA6B,OAAO,sCAAsC,CAAC;AAAA,EACzI;AACD;AAEO,IAAM,YAAiC;AAAA,EAC7C,IAAI,OAAe;AAClB,WAAO,OAAO,MAAM,KAAK,IACtB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,uBAAuB,wBAAwB,OAAO,kBAAkB,CAAC;AAAA,EACpH;AACD;AAEO,IAAM,eAAoC;AAAA,EAChD,IAAI,OAAe;AAClB,WAAO,OAAO,MAAM,KAAK,IACtB,OAAO,IAAI,IAAI,wBAAwB,0BAA0B,wBAAwB,OAAO,kBAAkB,CAAC,IACnH,OAAO,GAAG,KAAK;AAAA,EACnB;AACD;AAEO,SAAS,kBAAkB,SAAsC;AACvE,QAAM,WAAW,cAAc,OAAO;AACtC,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,QAAQ,YAAY,IACxB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,wBAAwB,2BAA2B,OAAO,QAAQ,CAAC;AAAA,IAC9G;AAAA,EACD;AACD;AATgB;;;ACzFT,IAAM,mBAAN,MAAM,yBAA0C,cAAiB;AAAA,EAChE,SAAS,QAAsB;AACrC,WAAO,KAAK,cAAc,eAAe,MAAM,CAAmB;AAAA,EACnE;AAAA,EAEO,gBAAgB,QAAsB;AAC5C,WAAO,KAAK,cAAc,sBAAsB,MAAM,CAAmB;AAAA,EAC1E;AAAA,EAEO,YAAY,QAAsB;AACxC,WAAO,KAAK,cAAc,kBAAkB,MAAM,CAAmB;AAAA,EACtE;AAAA,EAEO,mBAAmB,QAAsB;AAC/C,WAAO,KAAK,cAAc,yBAAyB,MAAM,CAAmB;AAAA,EAC7E;AAAA,EAEO,MAAwB,QAA+B;AAC7D,WAAO,OAAO,MAAM,MAAM,IACtB,KAAK,cAAc,SAA2B,IAC9C,KAAK,cAAc,YAAY,MAAM,CAAmB;AAAA,EAC7D;AAAA,EAEO,SAAS,QAAsB;AACrC,WAAO,OAAO,MAAM,MAAM,IACvB,KAAK,cAAc,YAA8B,IACjD,KAAK,cAAc,eAAe,MAAM,CAAmB;AAAA,EAC/D;AAAA,EAEA,IAAW,MAAY;AACtB,WAAO,KAAK,cAAc,SAA2B;AAAA,EACtD;AAAA,EAEA,IAAW,UAAgB;AAC1B,WAAO,KAAK,cAAc,aAA+B;AAAA,EAC1D;AAAA,EAEA,IAAW,SAAe;AACzB,WAAO,KAAK,cAAc,YAA8B;AAAA,EACzD;AAAA,EAEA,IAAW,WAAiB;AAC3B,WAAO,KAAK,mBAAmB,CAAC;AAAA,EACjC;AAAA,EAEA,IAAW,WAAiB;AAC3B,WAAO,KAAK,SAAS,CAAC;AAAA,EACvB;AAAA,EAEO,YAAY,SAAuB;AACzC,WAAO,KAAK,cAAc,kBAAkB,OAAO,CAAmB;AAAA,EACvE;AAAA,EAEA,IAAW,MAAY;AACtB,WAAO,KAAK,UAAU,KAAK,GAA2B;AAAA,EACvD;AAAA,EAEA,IAAW,OAAa;AACvB,WAAO,KAAK,UAAU,KAAK,IAA4B;AAAA,EACxD;AAAA,EAEA,IAAW,QAAc;AACxB,WAAO,KAAK,UAAU,KAAK,KAA6B;AAAA,EACzD;AAAA,EAEA,IAAW,QAAc;AACxB,WAAO,KAAK,UAAU,KAAK,KAA6B;AAAA,EACzD;AAAA,EAEA,IAAW,SAAe;AACzB,WAAO,KAAK,UAAU,KAAK,MAA8B;AAAA,EAC1D;AAAA,EAEA,IAAW,QAAc;AACxB,WAAO,KAAK,UAAU,KAAK,KAA6B;AAAA,EACzD;AAAA,EAEA,IAAW,OAAa;AACvB,WAAO,KAAK,UAAU,KAAK,IAA4B;AAAA,EACxD;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,WACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,YAAY,+BAA+B,KAAK,CAAC;AAAA,EACpF;AACD;AAtFwE;AAAjE,IAAM,kBAAN;;;AChBA,IAAM,wBAAN,MAAM,8BAA6B,UAAU;AAAA,EAG5C,YAAY,UAAuB;AACzC,UAAM,gCAAgC;AACtC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEO,SAAS;AACf,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,WAAW,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG,QAAQ;AACnE,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,0BAA0B,QAAQ,KAAK,SAAS;AAAA,IACxE;AAEA,UAAM,SAAS,GAAG,QAAQ,QAAQ,wBAAwB,SAAS,CAAC,MAAM,QAAQ;AAClF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAC/B;AACD;AAzBoD;AAA7C,IAAM,uBAAN;;;ACHP,SAAS,WAAAA,gBAA4C;AAG9C,IAAM,wBAAN,MAAM,8BAA6B,UAAU;AAAA,EAI5C,YAAY,UAAuB,OAAgB;AACzD,UAAM,8BAA8B;AAEpC,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACd;AAAA,EAEO,SAAS;AACf,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,MACf,OAAO,KAAK;AAAA,IACb;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,WAAW,QAAQ,QAAQ,KAAK,SAAS,SAAS,GAAG,QAAQ;AACnE,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,0BAA0B,QAAQ,KAAK,SAAS;AAAA,IACxE;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQA,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,wBAAwB,SAAS,CAAC,MAAM,QAAQ;AAClF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,UAAU;AAAA,EAC9C;AACD;AAnCoD;AAA7C,IAAM,uBAAN;;;ACGA,IAAM,oBAAN,MAAM,0BAA4B,cAAiB;AAAA,EAIlD,YAAY,WAA6B,OAAsB,cAAyC,CAAC,GAAG;AAClH,UAAM,WAAW;AACjB,SAAK,YAAY;AACjB,SAAK,eAAe;AAAA,EACrB;AAAA,EAEgB,QAAQ,OAAuG;AAC9H,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,eAAe;AACrB,WAAO;AAAA,EACR;AAAA,EAEU,OAAO,OAA2C;AAC3D,WAAO,OAAO,UAAU,cACrB,OAAO,GAAG,SAAS,KAAK,YAAY,CAAC,IACrC,KAAK,UAAU,QAAQ,EAAE,KAAK;AAAA,EAClC;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,cAAc,KAAK,WAAW,CAAC;AAAA,EACjG;AACD;AAzB0D;AAAnD,IAAM,mBAAN;;;ACHA,IAAM,iBAAN,MAAM,uBAAsB,UAAU;AAAA,EAGrC,YAAY,QAA8B;AAChD,UAAM,6BAA6B;AAEnC,SAAK,SAAS;AAAA,EACf;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,mBAAmB,SAAS;AAAA,IACpD;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,GAAG,SAAS,KAAK;AAE1G,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AAExD,UAAM,SAAS,GAAG,QAAQ,QAAQ,iBAAiB,SAAS,CAAC,KAAK,QAAQ,QAAQ,KAAK,OAAO,OAAO,SAAS,GAAG,QAAQ,CAAC;AAC1H,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,SAAS,KAAK,OAClB,IAAI,CAAC,OAAO,MAAM;AAClB,YAAM,QAAQ,QAAQ,SAAS,IAAI,GAAG,SAAS,GAAG,QAAQ;AAC1D,YAAM,OAAO,MAAM,4BAA4B,EAAE,QAAQ,GAAG,UAAU,EAAE,QAAQ,OAAO,OAAO;AAE9F,aAAO,KAAK,KAAK,IAAI,IAAI;AAAA,IAC1B,CAAC,EACA,KAAK,MAAM;AACb,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA;AAAA,EAAO,MAAM;AAAA,EAC5C;AACD;AA9B6C;AAAtC,IAAM,gBAAN;;;ACIA,IAAM,kBAAN,MAAM,wBAA0B,cAAiB;AAAA,EAGhD,YAAY,YAAyC,cAAyC,CAAC,GAAG;AACxG,UAAM,WAAW;AACjB,SAAK,aAAa;AAAA,EACnB;AAAA,EAEA,IAAoB,WAA0C;AAC7D,QAAI,KAAK,WAAW,WAAW;AAAG,aAAO,IAAI,gBAA8B,CAAC,IAAI,iBAAiB,MAAS,CAAC,GAAG,KAAK,WAAW;AAE9H,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAE1C,UAAI,UAAU,aAAa;AAAW,eAAO,KAAK,MAAM;AAGxD,UAAI,UAAU,aAAa,MAAM;AAChC,eAAO,IAAI;AAAA,UACV,CAAC,IAAI,iBAAiB,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,UACpD,KAAK;AAAA,QACN;AAAA,MACD;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AAEjD,aAAO,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,IAAI,gBAAe,CAAC,IAAI,iBAAiB,MAAS,GAAG,GAAG,KAAK,UAAU,CAAC;AAAA,EAChF;AAAA,EAEA,IAAW,WAAkD;AAG5D,QAAI,KAAK,WAAW,WAAW;AAAG,aAAO,KAAK,MAAM;AAEpD,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAC1C,UAAI,UAAU,aAAa;AAAW,eAAO,IAAI,gBAAe,KAAK,WAAW,MAAM,CAAC,GAAG,KAAK,WAAW;AAAA,IAC3G,WAAW,qBAAqB,kBAAkB;AACjD,aAAO,IAAI,gBAAe,CAAC,IAAI,iBAAiB,IAAI,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC,GAAG,KAAK,WAAW;AAAA,IACtG;AAEA,WAAO,KAAK,MAAM;AAAA,EACnB;AAAA,EAEA,IAAoB,WAAqC;AACxD,QAAI,KAAK,WAAW,WAAW;AAAG,aAAO,IAAI,gBAAyB,CAAC,IAAI,iBAAiB,IAAI,CAAC,GAAG,KAAK,WAAW;AAEpH,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAE1C,UAAI,UAAU,aAAa;AAAM,eAAO,KAAK,MAAM;AAGnD,UAAI,UAAU,aAAa,QAAW;AACrC,eAAO,IAAI;AAAA,UACV,CAAC,IAAI,iBAAiB,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC;AAAA,UACpD,KAAK;AAAA,QACN;AAAA,MACD;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AAEjD,aAAO,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,IAAI,gBAAe,CAAC,IAAI,iBAAiB,IAAI,GAAG,GAAG,KAAK,UAAU,CAAC;AAAA,EAC3E;AAAA,EAEA,IAAoB,UAAgD;AACnE,QAAI,KAAK,WAAW,WAAW;AAAG,aAAO,IAAI,gBAAqC,CAAC,IAAI,iBAAiB,CAAC,GAAG,KAAK,WAAW;AAE5H,UAAM,CAAC,SAAS,IAAI,KAAK;AACzB,QAAI,qBAAqB,kBAAkB;AAE1C,UAAI,UAAU,aAAa,QAAQ,UAAU,aAAa,QAAW;AACpE,eAAO,IAAI,gBAAqC,CAAC,IAAI,iBAAiB,GAAG,GAAG,KAAK,WAAW,MAAM,CAAC,CAAC,GAAG,KAAK,WAAW;AAAA,MACxH;AAAA,IACD,WAAW,qBAAqB,kBAAkB;AAEjD,aAAO,KAAK,MAAM;AAAA,IACnB;AAEA,WAAO,IAAI,gBAAqC,CAAC,IAAI,iBAAiB,GAAG,GAAG,KAAK,UAAU,CAAC;AAAA,EAC7F;AAAA,EAEgB,MAAS,YAAgE;AACxF,WAAO,IAAI,gBAAsB,CAAC,GAAG,KAAK,YAAY,GAAG,UAAU,CAAC;AAAA,EACrE;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,WAAW,CAAC;AAAA,EAC/E;AAAA,EAEU,OAAO,OAA4D;AAC5E,UAAM,SAAsB,CAAC;AAE7B,eAAW,aAAa,KAAK,YAAY;AACxC,YAAM,SAAS,UAAU,IAAI,KAAK;AAClC,UAAI,OAAO,KAAK;AAAG,eAAO;AAC1B,aAAO,KAAK,OAAO,KAAM;AAAA,IAC1B;AAEA,WAAO,OAAO,IAAI,IAAI,cAAc,MAAM,CAAC;AAAA,EAC5C;AACD;AAzGwD;AAAjD,IAAM,iBAAN;;;ACOA,IAAM,mBAAN,MAAM,yBAAsE,cAAiB;AAAA,EAU5F,YACN,OACA,WAAoC,gBACpC,cAAyC,CAAC,GACzC;AACD,UAAM,WAAW;AAZlB,SAAiB,OAA6B,CAAC;AAG/C,SAAiB,eAAe,oBAAI,IAAqC;AACzE,SAAiB,wBAAwB,oBAAI,IAAqC;AAClF,SAAiB,oCAAoC,oBAAI,IAAwC;AAQhG,SAAK,QAAQ;AACb,SAAK,WAAW;AAEhB,YAAQ,KAAK,UAAU;AAAA,MACtB,KAAK;AACJ,aAAK,iBAAiB,CAAC,UAAU,KAAK,qBAAqB,KAAK;AAChE;AAAA,MACD,KAAK,gBAAgC;AACpC,aAAK,iBAAiB,CAAC,UAAU,KAAK,qBAAqB,KAAK;AAChE;AAAA,MACD;AAAA,MACA,KAAK;AACJ,aAAK,iBAAiB,CAAC,UAAU,KAAK,0BAA0B,KAAK;AACrE;AAAA,IACF;AAEA,UAAM,eAAe,OAAO,QAAQ,KAAK;AACzC,SAAK,OAAO,aAAa,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG;AAE3C,eAAW,CAAC,KAAK,SAAS,KAAK,cAAc;AAC5C,UAAI,qBAAqB,gBAAgB;AAExC,cAAM,CAAC,iCAAiC,IAAI,UAAU,YAAY;AAElE,YAAI,6CAA6C,kBAAkB;AAClE,eAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,QAC9C,WAAW,6CAA6C,kBAAkB;AACzE,cAAI,kCAAkC,aAAa,QAAW;AAC7D,iBAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,UAC9C,OAAO;AACN,iBAAK,aAAa,IAAI,KAAK,SAAS;AAAA,UACrC;AAAA,QACD,WAAW,qBAAqB,kBAAkB;AACjD,eAAK,kCAAkC,IAAI,KAAK,SAAS;AAAA,QAC1D,OAAO;AACN,eAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACrC;AAAA,MACD,WAAW,qBAAqB,kBAAkB;AACjD,aAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,MAC9C,WAAW,qBAAqB,kBAAkB;AACjD,YAAI,UAAU,aAAa,QAAW;AACrC,eAAK,sBAAsB,IAAI,KAAK,SAAS;AAAA,QAC9C,OAAO;AACN,eAAK,aAAa,IAAI,KAAK,SAAS;AAAA,QACrC;AAAA,MACD,WAAW,qBAAqB,kBAAkB;AACjD,aAAK,kCAAkC,IAAI,KAAK,SAAS;AAAA,MAC1D,OAAO;AACN,aAAK,aAAa,IAAI,KAAK,SAAS;AAAA,MACrC;AAAA,IACD;AAAA,EACD;AAAA,EAEA,IAAW,SAAe;AACzB,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,gBAAgC,KAAK,WAAW,CAAC;AAAA,EAC1G;AAAA,EAEA,IAAW,SAAe;AACzB,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,gBAAgC,KAAK,WAAW,CAAC;AAAA,EAC1G;AAAA,EAEA,IAAW,cAAoB;AAC9B,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,qBAAqC,KAAK,WAAW,CAAC;AAAA,EAC/G;AAAA,EAEA,IAAW,UAA0D;AACpE,UAAM,QAAQ,OAAO,YAAY,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM,GAAyC,EAAE,QAAQ,CAAC,CAAC;AAC9H,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EACpF;AAAA,EAEA,IAAW,WAA4D;AACtE,UAAM,QAAQ,OAAO;AAAA,MACpB,KAAK,KAAK,IAAI,CAAC,QAAQ;AACtB,YAAI,YAAY,KAAK,MAAM,GAAyC;AACpE,YAAI,qBAAqB;AAAgB,sBAAY,UAAU;AAC/D,eAAO,CAAC,KAAK,SAAS;AAAA,MACvB,CAAC;AAAA,IACF;AACA,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EACpF;AAAA,EAEO,OAA0B,QAAkF;AAClH,UAAM,QAAQ,EAAE,GAAG,KAAK,OAAO,GAAI,kBAAkB,mBAAkB,OAAO,QAAQ,OAAQ;AAC9F,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EACpF;AAAA,EAEO,KAAwB,MAA4E;AAC1G,UAAM,QAAQ,OAAO;AAAA,MACpB,KAAK,OAAO,CAAC,QAAQ,KAAK,KAAK,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM,GAAyC,CAAC,CAAC;AAAA,IACxH;AACA,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EACpF;AAAA,EAEO,KAAwB,MAA4E;AAC1G,UAAM,QAAQ,OAAO;AAAA,MACpB,KAAK,KAAK,OAAO,CAAC,QAAQ,CAAC,KAAK,SAAS,GAAU,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM,GAAyC,CAAC,CAAC;AAAA,IAChI;AACA,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,OAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EACpF;AAAA,EAEmB,OAAO,OAAoE;AAC7F,UAAM,cAAc,OAAO;AAC3B,QAAI,gBAAgB,UAAU;AAC7B,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,oDAAoD,WAAW,YAAY,KAAK,CAAC;AAAA,IACvI;AAEA,QAAI,UAAU,MAAM;AACnB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,qCAAqC,KAAK,CAAC;AAAA,IACjG;AAEA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,yCAAyC,KAAK,CAAC;AAAA,IACrG;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,KAAU;AAAA,IAC5B;AAEA,eAAW,aAAa,OAAO,OAAO,KAAK,KAAK,GAA2B;AAC1E,gBAAU,UAAU,KAAK,UAAU,KAAM;AAAA,IAC1C;AAEA,WAAO,KAAK,eAAe,KAAe;AAAA,EAC3C;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,OAAO,KAAK,UAAU,KAAK,WAAW,CAAC;AAAA,EACzF;AAAA,EAEQ,qBAAqB,OAAiD;AAC7E,UAAM,SAAqC,CAAC;AAC5C,UAAM,cAAc,CAAC;AACrB,UAAM,eAAe,IAAI,IAAI,OAAO,QAAQ,KAAK,CAAyB;AAE1E,UAAM,eAAe,wBAAC,KAAc,cAAsC;AACzE,YAAM,SAAS,UAAU,IAAI,MAAM,GAAmB,CAAC;AAEvD,UAAI,OAAO,KAAK,GAAG;AAClB,oBAAY,GAAG,IAAI,OAAO;AAAA,MAC3B,OAAO;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB;AAAA,IACD,GATqB;AAWrB,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,cAAc;AACjD,UAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,qBAAa,KAAK,SAAS;AAAA,MAC5B,OAAO;AACN,eAAO,KAAK,CAAC,KAAK,IAAI,qBAAqB,GAAG,CAAC,CAAC;AAAA,MACjD;AAAA,IACD;AAGA,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,mCAAmC;AACtE,mBAAa,OAAO,GAAG;AACvB,mBAAa,KAAK,SAAS;AAAA,IAC5B;AAGA,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,IAChD;AAIA,UAAM,uCAAuC,KAAK,sBAAsB,OAAO,aAAa;AAE5F,QAAI,sCAAsC;AACzC,iBAAW,CAAC,GAAG,KAAK,cAAc;AACjC,cAAM,YAAY,KAAK,sBAAsB,IAAI,GAAG;AAEpD,YAAI,WAAW;AACd,uBAAa,KAAK,SAAS;AAAA,QAC5B;AAAA,MACD;AAAA,IACD,OAAO;AACN,iBAAW,CAAC,KAAK,SAAS,KAAK,KAAK,uBAAuB;AAC1D,YAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,uBAAa,KAAK,SAAS;AAAA,QAC5B;AAAA,MACD;AAAA,IACD;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,EAChD;AAAA,EAEQ,qBAAqB,OAAiD;AAC7E,UAAM,SAAqC,CAAC;AAC5C,UAAM,cAAc,CAAC;AACrB,UAAM,eAAe,IAAI,IAAI,OAAO,QAAQ,KAAK,CAAyB;AAE1E,UAAM,eAAe,wBAAC,KAAc,cAAsC;AACzE,YAAM,SAAS,UAAU,IAAI,MAAM,GAAmB,CAAC;AAEvD,UAAI,OAAO,KAAK,GAAG;AAClB,oBAAY,GAAG,IAAI,OAAO;AAAA,MAC3B,OAAO;AACN,cAAM,QAAQ,OAAO;AACrB,eAAO,KAAK,CAAC,KAAK,KAAK,CAAC;AAAA,MACzB;AAAA,IACD,GATqB;AAWrB,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,cAAc;AACjD,UAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,qBAAa,KAAK,SAAS;AAAA,MAC5B,OAAO;AACN,eAAO,KAAK,CAAC,KAAK,IAAI,qBAAqB,GAAG,CAAC,CAAC;AAAA,MACjD;AAAA,IACD;AAGA,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,mCAAmC;AACtE,mBAAa,OAAO,GAAG;AACvB,mBAAa,KAAK,SAAS;AAAA,IAC5B;AAEA,eAAW,CAAC,KAAK,SAAS,KAAK,KAAK,uBAAuB;AAG1D,UAAI,aAAa,SAAS,GAAG;AAC5B;AAAA,MACD;AAEA,UAAI,aAAa,OAAO,GAAG,GAAG;AAC7B,qBAAa,KAAK,SAAS;AAAA,MAC5B;AAAA,IACD;AAEA,QAAI,aAAa,SAAS,GAAG;AAC5B,iBAAW,CAAC,KAAKC,MAAK,KAAK,aAAa,QAAQ,GAAG;AAClD,eAAO,KAAK,CAAC,KAAK,IAAI,qBAAqB,KAAKA,MAAK,CAAC,CAAC;AAAA,MACxD;AAAA,IACD;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,EAChD;AAAA,EAEQ,0BAA0B,OAAiD;AAClF,UAAM,SAAS,KAAK,qBAAqB,KAAK;AAC9C,WAAO,OAAO,MAAM,IAAI,SAAS,OAAO,GAAG,EAAE,GAAG,OAAO,GAAG,OAAO,MAAM,CAAM;AAAA,EAC9E;AACD;AAxQoG;AAA7F,IAAM,kBAAN;;;ACVA,IAAM,wBAAN,MAAM,8BAAsD,cAAiB;AAAA,EACzE,OAAO,OAA4C;AAC5D,WAAO,OAAO,GAAG,KAAU;AAAA,EAC5B;AACD;AAJoF;AAA7E,IAAM,uBAAN;;;ACGA,IAAM,mBAAN,MAAM,yBAA2B,cAAiC;AAAA,EAGjE,YAAY,WAA6B,cAAyD,CAAC,GAAG;AAC5G,UAAM,WAAW;AACjB,SAAK,YAAY;AAAA,EAClB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,WAAW,CAAC;AAAA,EAC9E;AAAA,EAEU,OAAO,OAAoF;AACpG,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,sBAAsB,KAAK,CAAC;AAAA,IAClF;AAEA,QAAI,UAAU,MAAM;AACnB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,qCAAqC,KAAK,CAAC;AAAA,IACjG;AAEA,QAAI,MAAM,QAAQ,KAAK,GAAG;AACzB,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,yCAAyC,KAAK,CAAC;AAAA,IACrG;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,KAA0B;AAAA,IAC5C;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAiC,CAAC;AAExC,eAAW,CAAC,KAAK,GAAG,KAAK,OAAO,QAAQ,KAAM,GAAG;AAChD,YAAM,SAAS,KAAK,UAAU,IAAI,GAAG;AACrC,UAAI,OAAO,KAAK;AAAG,oBAAY,GAAG,IAAI,OAAO;AAAA;AACxC,eAAO,KAAK,CAAC,KAAK,OAAO,KAAM,CAAC;AAAA,IACtC;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,EAChD;AACD;AA1CyE;AAAlE,IAAM,kBAAN;;;ACAA,IAAM,gBAAN,MAAM,sBAAwB,cAAsB;AAAA,EAGnD,YAAY,WAA6B,cAA8C,CAAC,GAAG;AACjG,UAAM,WAAW;AACjB,SAAK,YAAY;AAAA,EAClB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,WAAW,CAAC;AAAA,EAC9E;AAAA,EAEU,OAAO,QAAkE;AAClF,QAAI,EAAE,kBAAkB,MAAM;AAC7B,aAAO,OAAO,IAAI,IAAI,gBAAgB,YAAY,kBAAkB,MAAM,CAAC;AAAA,IAC5E;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,MAAM;AAAA,IACxB;AAEA,UAAM,SAAsB,CAAC;AAC7B,UAAM,cAAc,oBAAI,IAAO;AAE/B,eAAW,SAAS,QAAQ;AAC3B,YAAM,SAAS,KAAK,UAAU,IAAI,KAAK;AACvC,UAAI,OAAO,KAAK;AAAG,oBAAY,IAAI,OAAO,KAAK;AAAA;AAC1C,eAAO,KAAK,OAAO,KAAM;AAAA,IAC/B;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,cAAc,MAAM,CAAC;AAAA,EACxC;AACD;AAlC2D;AAApD,IAAM,eAAN;;;ACFP,IAAM,eAAe;AAqBd,SAAS,cAAc,OAAwB;AAIrD,MAAI,CAAC;AAAO,WAAO;AAGnB,QAAM,UAAU,MAAM,QAAQ,GAAG;AAKjC,MAAI,YAAY;AAAI,WAAO;AAO3B,MAAI,UAAU;AAAI,WAAO;AAEzB,QAAM,cAAc,UAAU;AAK9B,MAAI,MAAM,SAAS,KAAK,WAAW;AAAG,WAAO;AAO7C,MAAI,MAAM,SAAS,cAAc;AAAK,WAAO;AAG7C,MAAI,WAAW,MAAM,QAAQ,KAAK,WAAW;AAM7C,MAAI,aAAa;AAAI,WAAO;AAgB5B,MAAI,eAAe;AACnB,KAAG;AACF,QAAI,WAAW,eAAe;AAAI,aAAO;AAEzC,mBAAe,WAAW;AAAA,EAC3B,UAAU,WAAW,MAAM,QAAQ,KAAK,YAAY,OAAO;AAI3D,MAAI,MAAM,SAAS,eAAe;AAAI,WAAO;AAY7C,SAAO,aAAa,KAAK,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,oBAAoB,MAAM,MAAM,WAAW,CAAC;AAClG;AAhFgB;AAkFhB,SAAS,oBAAoB,QAAyB;AACrD,MAAI;AACH,WAAO,IAAI,IAAI,UAAU,MAAM,EAAE,EAAE,aAAa;AAAA,EACjD,QAAQ;AACP,WAAO;AAAA,EACR;AACD;AANS;;;ACvGT,IAAM,QAAQ;AACd,IAAM,QAAQ,IAAI,KAAK,UAAU,KAAK;AACtC,IAAM,UAAU,IAAI,OAAO,IAAI,KAAK,GAAG;AAGvC,IAAM,QAAQ;AACd,IAAM,UAAU,IAAI;AAAA,EACnB,QACO,KAAK,WAAW,KAAK,UACrB,KAAK,WAAW,KAAK,KAAK,KAAK,UAC/B,KAAK,YAAY,KAAK,MAAM,KAAK,gBACjC,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,gBACjD,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,gBACjD,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,gBACjD,KAAK,aAAa,KAAK,UAAU,KAAK,MAAM,KAAK,sBAC3C,KAAK,UAAU,KAAK,QAAQ,KAAK;AAE/C;AAEO,SAAS,OAAOC,IAAoB;AAC1C,SAAO,QAAQ,KAAKA,EAAC;AACtB;AAFgB;AAIT,SAAS,OAAOA,IAAoB;AAC1C,SAAO,QAAQ,KAAKA,EAAC;AACtB;AAFgB;AAIT,SAAS,KAAKA,IAAmB;AACvC,MAAI,OAAOA,EAAC;AAAG,WAAO;AACtB,MAAI,OAAOA,EAAC;AAAG,WAAO;AACtB,SAAO;AACR;AAJgB;;;AChCT,IAAM,mBAAmB;AAEzB,SAAS,oBAAoB,OAAe;AAClD,SAAO,iBAAiB,KAAK,KAAK;AACnC;AAFgB;;;ACFhB,SAAS,WAAAF,gBAA4C;AAI9C,IAAM,wCAAN,MAAM,8CAA0D,oBAAuB;AAAA,EAGtF,YAAY,YAAkC,SAAiB,OAAU,UAA6B;AAC5G,UAAM,YAAY,SAAS,KAAK;AAChC,SAAK,WAAW;AAAA,EACjB;AAAA,EAEO,SAAS;AACf,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,YAAY,KAAK;AAAA,MACjB,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,aAAa,QAAQ,QAAQ,KAAK,YAAY,QAAQ;AAC5D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,0CAA0C,UAAU,KAAK,SAAS;AAAA,IAC1F;AAEA,UAAM,aAAa,EAAE,GAAG,SAAS,OAAO,QAAQ,UAAU,OAAO,OAAO,QAAQ,QAAS,EAAE;AAE3F,UAAM,eAAe,QAAQ,QAAQ,KAAK,WAAW;AACrD,UAAM,UAAU;AAAA,IAAO,YAAY;AACnC,UAAM,QAAQA,SAAQ,KAAK,OAAO,UAAU,EAAE,QAAQ,OAAO,OAAO;AAEpE,UAAM,SAAS,GAAG,QAAQ,QAAQ,wCAAwC,SAAS,CAAC,MAAM,UAAU;AACpG,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AAEtD,UAAM,kBAAkB;AAAA,IAAO,YAAY;AAC3C,UAAM,gBAAgB;AAAA,IAAO,QAAQ,QAAQ,kCAAkC,QAAQ,CAAC,GAAG,eAAe,GAAG,KAAK,SAChH,IAAI,CAAC,aAAa,QAAQ,QAAQ,UAAU,SAAS,CAAC,EACtD,KAAK,eAAe,CAAC;AACvB,UAAM,aAAa;AAAA,IAAO,QAAQ,QAAQ,aAAa,QAAQ,CAAC,GAAG,OAAO,GAAG,KAAK;AAClF,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,aAAa;AAAA,EAAK,UAAU;AAAA,EAChE;AACD;AAvC8F;AAAvF,IAAM,uCAAN;;;ACJA,SAAS,mBAAwD,KAAqC;AAC5G,UAAQ,IAAI,QAAQ;AAAA,IACnB,KAAK;AACJ,aAAO,MAAM;AAAA,IACd,KAAK;AACJ,aAAO,IAAI,CAAC;AAAA,IACb,KAAK,GAAG;AACP,YAAM,CAAC,KAAK,GAAG,IAAI;AACnB,aAAO,IAAI,WAAW,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,MAAM;AAAA,IACtD;AAAA,IACA,SAAS;AACR,aAAO,IAAI,WAAW;AACrB,mBAAW,MAAM,KAAK;AACrB,gBAAM,SAAS,GAAG,GAAG,MAAM;AAC3B,cAAI;AAAQ,mBAAO;AAAA,QACpB;AAEA,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACD;AArBgB;;;ACYT,SAAS,oBAAoB,SAAsB;AACzD,QAAM,MAA0F,CAAC;AAEjG,MAAI,SAAS,kBAAkB;AAAQ,QAAI,KAAK,mBAAmB,QAAQ,gBAAgB,CAAC;AAC5F,MAAI,SAAS,gBAAgB;AAAQ,QAAI,KAAK,iBAAiB,QAAQ,cAAc,CAAC;AAEtF,SAAO,gBAAgB,GAAG,GAAG;AAC9B;AAPgB;AAShB,SAAS,mBAAmB,kBAAoC;AAC/D,SAAO,CAAC,OAAe,QACtB,iBAAiB,SAAS,IAAI,QAA0B,IACrD,OACA,IAAI,qCAAqC,gBAAgB,wBAAwB,OAAO,gBAAgB;AAC7G;AALS;AAOT,SAAS,iBAAiB,gBAAgC;AACzD,SAAO,CAAC,OAAe,QACtB,eAAe,SAAS,IAAI,QAAwB,IACjD,OACA,IAAI,qCAAqC,gBAAgB,sBAAsB,OAAO,cAAc;AACzG;AALS;;;ACOT,SAAS,uBAAuB,YAAwB,MAA4B,UAAkB,QAAqC;AAC1I,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,WAAW,MAAM,QAAQ,MAAM,IACnC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,yBAAyB,OAAO,QAAQ,CAAC;AAAA,IAC1F;AAAA,EACD;AACD;AARS;AAUF,SAAS,qBAAqB,QAAqC;AACzE,QAAM,WAAW,qBAAqB,MAAM;AAC5C,SAAO,uBAAuB,UAAU,2BAA2B,UAAU,MAAM;AACpF;AAHgB;AAKT,SAAS,4BAA4B,QAAqC;AAChF,QAAM,WAAW,sBAAsB,MAAM;AAC7C,SAAO,uBAAuB,iBAAiB,kCAAkC,UAAU,MAAM;AAClG;AAHgB;AAKT,SAAS,wBAAwB,QAAqC;AAC5E,QAAM,WAAW,qBAAqB,MAAM;AAC5C,SAAO,uBAAuB,aAAa,8BAA8B,UAAU,MAAM;AAC1F;AAHgB;AAKT,SAAS,+BAA+B,QAAqC;AACnF,QAAM,WAAW,sBAAsB,MAAM;AAC7C,SAAO,uBAAuB,oBAAoB,qCAAqC,UAAU,MAAM;AACxG;AAHgB;AAKT,SAAS,kBAAkB,QAAqC;AACtE,QAAM,WAAW,uBAAuB,MAAM;AAC9C,SAAO,uBAAuB,OAAO,wBAAwB,UAAU,MAAM;AAC9E;AAHgB;AAKT,SAAS,qBAAqB,QAAqC;AACzE,QAAM,WAAW,uBAAuB,MAAM;AAC9C,SAAO,uBAAuB,UAAU,2BAA2B,UAAU,MAAM;AACpF;AAHgB;AAKT,SAAS,cAAmC;AAClD,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,cAAc,KAAK,IACvB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,kBAAkB,yBAAyB,OAAO,iCAAiC,CAAC;AAAA,IAC/H;AAAA,EACD;AACD;AARgB;AAUhB,SAAS,qBAAqB,MAA4B,UAAkB,OAAoC;AAC/G,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,MAAM,KAAK,KAAK,IACpB,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,yBAAyB,OAAO,QAAQ,CAAC;AAAA,IAC1F;AAAA,EACD;AACD;AARS;AAUF,SAAS,UAAU,SAA2C;AACpE,QAAM,cAAc,oBAAoB,OAAO;AAC/C,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,UAAI;AACJ,UAAI;AACH,cAAM,IAAI,IAAI,KAAK;AAAA,MACpB,QAAQ;AACP,eAAO,OAAO,IAAI,IAAI,wBAAwB,gBAAgB,eAAe,OAAO,yBAAyB,CAAC;AAAA,MAC/G;AAEA,YAAM,oBAAoB,YAAY,OAAO,GAAG;AAChD,UAAI,sBAAsB;AAAM,eAAO,OAAO,GAAG,KAAK;AACtD,aAAO,OAAO,IAAI,iBAAiB;AAAA,IACpC;AAAA,EACD;AACD;AAhBgB;AAkBT,SAAS,SAAS,SAAsC;AAC9D,QAAM,YAAY,UAAW,IAAI,OAAO,KAAe;AACvD,QAAM,cAAc,YAAY,IAAI,SAAS,YAAY,IAAI,SAAS;AAEtE,QAAM,OAAO,cAAc,SAAS;AACpC,QAAM,UAAU,aAAa,SAAS;AACtC,QAAM,WAAW,uBAAuB,SAAS;AACjD,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,YAAY,KAAK,IAAI,OAAO,GAAG,KAAK,IAAI,OAAO,IAAI,IAAI,wBAAwB,MAAM,SAAS,OAAO,QAAQ,CAAC;AAAA,IACtH;AAAA,EACD;AACD;AAZgB;AAcT,SAAS,YAAY,OAAe;AAC1C,SAAO,qBAAqB,kBAAkB,YAAY,KAAK,8BAA8B,KAAK;AACnG;AAFgB;AAIT,SAAS,WAAW,EAAE,UAAU,GAAG,WAAW,MAAM,IAAuB,CAAC,GAAG;AACrF,wBAAY;AACZ,QAAM,QAAQ,IAAI;AAAA,IACjB,gCAAgC,OAAO,8CACtC,WAAW,0CAA0C,EACtD;AAAA,IACA;AAAA,EACD;AACA,QAAM,WAAW,yBAAyB,OAAO,YAAY,WAAW,IAAI,OAAO,KAAK,gBAAgB,OAAO,EAAE;AACjH,SAAO,qBAAqB,iBAAiB,UAAU,KAAK;AAC7D;AAVgB;AAYT,SAAS,aAAkC;AACjD,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,YAAM,OAAO,KAAK,MAAM,KAAK;AAE7B,aAAO,OAAO,MAAM,IAAI,IACrB,OAAO;AAAA,QACP,IAAI;AAAA,UACH;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACA,IACA,OAAO,GAAG,KAAK;AAAA,IACnB;AAAA,EACD;AACD;AAjBgB;AAmBT,SAAS,cAAmC;AAClD,SAAO;AAAA,IACN,IAAI,OAAe;AAClB,aAAO,oBAAoB,KAAK,IAC7B,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,kBAAkB,wBAAwB,OAAO,+BAA+B,CAAC;AAAA,IAC5H;AAAA,EACD;AACD;AARgB;;;AC5IT,IAAM,mBAAN,MAAM,yBAA0C,cAAiB;AAAA,EAChE,eAAe,QAAsB;AAC3C,WAAO,KAAK,cAAc,qBAAqB,MAAM,CAAmB;AAAA,EACzE;AAAA,EAEO,sBAAsB,QAAsB;AAClD,WAAO,KAAK,cAAc,4BAA4B,MAAM,CAAmB;AAAA,EAChF;AAAA,EAEO,kBAAkB,QAAsB;AAC9C,WAAO,KAAK,cAAc,wBAAwB,MAAM,CAAmB;AAAA,EAC5E;AAAA,EAEO,yBAAyB,QAAsB;AACrD,WAAO,KAAK,cAAc,+BAA+B,MAAM,CAAmB;AAAA,EACnF;AAAA,EAEO,YAAY,QAAsB;AACxC,WAAO,KAAK,cAAc,kBAAkB,MAAM,CAAmB;AAAA,EACtE;AAAA,EAEO,eAAe,QAAsB;AAC3C,WAAO,KAAK,cAAc,qBAAqB,MAAM,CAAmB;AAAA,EACzE;AAAA,EAEA,IAAW,QAAc;AACxB,WAAO,KAAK,cAAc,YAAY,CAAmB;AAAA,EAC1D;AAAA,EAEO,IAAI,SAA4B;AACtC,WAAO,KAAK,cAAc,UAAU,OAAO,CAAmB;AAAA,EAC/D;AAAA,EAEO,KAAK,SAAmC;AAC9C,WAAO,KAAK,cAAc,WAAW,OAAO,CAAmB;AAAA,EAChE;AAAA,EAEO,MAAM,OAAqB;AACjC,WAAO,KAAK,cAAc,YAAY,KAAK,CAAmB;AAAA,EAC/D;AAAA,EAEA,IAAW,OAAO;AACjB,WAAO,KAAK,cAAc,WAAW,CAAmB;AAAA,EACzD;AAAA,EAEA,IAAW,OAAa;AACvB,WAAO,KAAK,GAAG,CAAC;AAAA,EACjB;AAAA,EAEA,IAAW,OAAa;AACvB,WAAO,KAAK,GAAG,CAAC;AAAA,EACjB;AAAA,EAEO,GAAG,SAAuB;AAChC,WAAO,KAAK,cAAc,SAAS,OAAO,CAAmB;AAAA,EAC9D;AAAA,EAEO,QAAc;AACpB,WAAO,KAAK,cAAc,YAAY,CAAmB;AAAA,EAC1D;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,OAAO,UAAU,WACrB,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,YAAY,+BAA+B,KAAK,CAAC;AAAA,EACpF;AACD;AAlEwE;AAAjE,IAAM,kBAAN;;;ACfA,IAAM,kBAAN,MAAM,wBAAwC,cAAsB;AAAA,EAGnE,YAAY,YAAqC,cAA8C,CAAC,GAAG;AACzG,UAAM,WAAW;AAHlB,SAAiB,aAAsC,CAAC;AAIvD,SAAK,aAAa;AAAA,EACnB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,YAAY,KAAK,WAAW,CAAC;AAAA,EAC/E;AAAA,EAEU,OAAO,QAA0E;AAC1F,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC3B,aAAO,OAAO,IAAI,IAAI,gBAAgB,cAAc,qBAAqB,MAAM,CAAC;AAAA,IACjF;AAEA,QAAI,OAAO,WAAW,KAAK,WAAW,QAAQ;AAC7C,aAAO,OAAO,IAAI,IAAI,gBAAgB,cAAc,+BAA+B,KAAK,WAAW,MAAM,IAAI,MAAM,CAAC;AAAA,IACrH;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,MAAgB;AAAA,IAClC;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAiB,CAAC;AAExB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,YAAM,SAAS,KAAK,WAAW,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC;AAC/C,UAAI,OAAO,KAAK;AAAG,oBAAY,KAAK,OAAO,KAAK;AAAA;AAC3C,eAAO,KAAK,CAAC,GAAG,OAAO,KAAM,CAAC;AAAA,IACpC;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,EAChD;AACD;AAtC2E;AAApE,IAAM,iBAAN;;;ACAA,IAAM,gBAAN,MAAM,sBAA2B,cAAyB;AAAA,EAIzD,YAAY,cAAgC,gBAAkC,cAAiD,CAAC,GAAG;AACzI,UAAM,WAAW;AACjB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAAA,EACvB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,cAAc,KAAK,gBAAgB,KAAK,WAAW,CAAC;AAAA,EACtG;AAAA,EAEU,OAAO,OAA4E;AAC5F,QAAI,EAAE,iBAAiB,MAAM;AAC5B,aAAO,OAAO,IAAI,IAAI,gBAAgB,eAAe,kBAAkB,KAAK,CAAC;AAAA,IAC9E;AAEA,QAAI,CAAC,KAAK,sBAAsB;AAC/B,aAAO,OAAO,GAAG,KAAK;AAAA,IACvB;AAEA,UAAM,SAAgC,CAAC;AACvC,UAAM,cAAc,oBAAI,IAAU;AAElC,eAAW,CAAC,KAAK,GAAG,KAAK,MAAM,QAAQ,GAAG;AACzC,YAAM,YAAY,KAAK,aAAa,IAAI,GAAG;AAC3C,YAAM,cAAc,KAAK,eAAe,IAAI,GAAG;AAC/C,YAAM,EAAE,OAAO,IAAI;AACnB,UAAI,UAAU,MAAM;AAAG,eAAO,KAAK,CAAC,KAAK,UAAU,KAAK,CAAC;AACzD,UAAI,YAAY,MAAM;AAAG,eAAO,KAAK,CAAC,KAAK,YAAY,KAAK,CAAC;AAC7D,UAAI,OAAO,WAAW;AAAQ,oBAAY,IAAI,UAAU,OAAQ,YAAY,KAAM;AAAA,IACnF;AAEA,WAAO,OAAO,WAAW,IACtB,OAAO,GAAG,WAAW,IACrB,OAAO,IAAI,IAAI,sBAAsB,MAAM,CAAC;AAAA,EAChD;AACD;AAvCiE;AAA1D,IAAM,eAAN;;;ACHA,IAAM,iBAAN,MAAM,uBAAuE,cAAiB;AAAA,EAG7F,YAAY,WAAkC,cAAyC,CAAC,GAAG;AACjG,UAAM,WAAW;AACjB,SAAK,YAAY;AAAA,EAClB;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,WAAW,KAAK,WAAW,CAAC;AAAA,EAC9E;AAAA,EAEU,OAAO,QAA4C;AAC5D,WAAO,KAAK,UAAU,MAAM,EAAE,IAAI,MAAM;AAAA,EACzC;AACD;AAfqG;AAA9F,IAAM,gBAAN;;;ACDA,IAAM,yBAAN,MAAM,+BAA8B,UAAU;AAAA,EAK7C,YAAY,OAAwB,MAAgB,cAAqD;AAC/G,UAAM,4DAA4D;AAElE,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EACrB;AAAA,EAEO,SAAS;AACf,WAAO;AAAA,MACN,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,cAAc,CAAC,GAAG,KAAK,aAAa,QAAQ,CAAC;AAAA,IAC9C;AAAA,EACD;AAAA,EAEA,CAAW,4BAA4B,EAAE,OAAe,SAAyC;AAChG,UAAM,QAAQ,QAAQ,QAAQ,KAAK,MAAM,SAAS,GAAG,QAAQ;AAC7D,QAAI,QAAQ,GAAG;AACd,aAAO,QAAQ,QAAQ,2BAA2B,KAAK,KAAK,SAAS;AAAA,IACtE;AAEA,UAAM,UAAU;AAAA,IAAO,QAAQ,QAAQ,KAAK,WAAW,CAAC;AACxD,UAAM,QAAQ,KAAK,SACjB,IAAI,CAAC,QAAQ;AACb,YAAM,YAAY,KAAK,aAAa,IAAI,GAAG;AAC3C,aAAO,GAAG,QAAQ,QAAQ,KAAK,QAAQ,CAAC,OAAO,QAAQ;AAAA,QACtD,UAAU,SAAS;AAAA,QACnB,OAAO,cAAc,WAAW,WAAW;AAAA,MAC5C,CAAC;AAAA,IACF,CAAC,EACA,KAAK,OAAO;AAEd,UAAM,SAAS,GAAG,QAAQ,QAAQ,yBAAyB,SAAS,CAAC,MAAM,KAAK;AAChF,UAAM,UAAU,QAAQ,QAAQ,KAAK,SAAS,QAAQ;AACtD,UAAM,aAAa,GAAG,OAAO,GAAG,KAAK;AACrC,WAAO,GAAG,MAAM;AAAA,IAAO,OAAO;AAAA,EAAK,UAAU;AAAA,EAC9C;AACD;AA5CqD;AAA9C,IAAM,wBAAN;;;ACEA,IAAM,uBAAN,MAAM,6BAAsD,cAA0B;AAAA,EAMrF,YAAY,WAAc;AAChC,UAAM;AALP,SAAgB,qBAA8B;AAE9C,SAAiB,cAAc,oBAAI,IAAiC;AAInE,SAAK,YAAY;AAEjB,SAAK,WAAW,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,QAAQ;AACtD,aAAO,OAAO,UAAU,UAAU,GAAG,CAAC,MAAM;AAAA,IAC7C,CAAC;AAED,eAAW,OAAO,KAAK,UAAU;AAChC,YAAM,YAAY,UAAU,GAAG;AAE/B,WAAK,YAAY,IAAI,KAAK,SAAS;AACnC,WAAK,YAAY,IAAI,WAAW,SAAS;AAEzC,UAAI,OAAO,cAAc,UAAU;AAClC,aAAK,qBAAqB;AAC1B,aAAK,YAAY,IAAI,GAAG,SAAS,IAAI,SAAS;AAAA,MAC/C;AAAA,IACD;AAAA,EACD;AAAA,EAEmB,OAAO,OAA6E;AACtG,UAAM,cAAc,OAAO;AAE3B,QAAI,gBAAgB,UAAU;AAC7B,UAAI,CAAC,KAAK,oBAAoB;AAC7B,eAAO,OAAO,IAAI,IAAI,gBAAgB,mBAAmB,qCAAqC,KAAK,CAAC;AAAA,MACrG;AAAA,IACD,WAAW,gBAAgB,UAAU;AAEpC,aAAO,OAAO,IAAI,IAAI,gBAAgB,mBAAmB,+CAA+C,KAAK,CAAC;AAAA,IAC/G;AAEA,UAAM,SAAS;AAEf,UAAM,oBAAoB,KAAK,YAAY,IAAI,MAAM;AAErD,WAAO,OAAO,sBAAsB,cACjC,OAAO,IAAI,IAAI,sBAAsB,QAAQ,KAAK,UAAU,KAAK,WAAW,CAAC,IAC7E,OAAO,GAAG,iBAAiB;AAAA,EAC/B;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,SAAS,CAAC;AAAA,EAC5D;AACD;AAnD6F;AAAtF,IAAM,sBAAN;;;ACYP,SAAS,+BACR,YACA,MACA,UACA,QACiB;AACjB,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,WAAW,MAAM,YAAY,MAAM,IACvC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,mCAAmC,OAAO,QAAQ,CAAC;AAAA,IACpG;AAAA,EACD;AACD;AAbS;AAeF,SAAS,6BAAmD,OAA+B;AACjG,QAAM,WAAW,yBAAyB,KAAK;AAC/C,SAAO,+BAA+B,UAAU,sCAAsC,UAAU,KAAK;AACtG;AAHgB;AAKT,SAAS,oCAA0D,OAA+B;AACxG,QAAM,WAAW,0BAA0B,KAAK;AAChD,SAAO,+BAA+B,iBAAiB,6CAA6C,UAAU,KAAK;AACpH;AAHgB;AAKT,SAAS,gCAAsD,OAA+B;AACpG,QAAM,WAAW,yBAAyB,KAAK;AAC/C,SAAO,+BAA+B,aAAa,yCAAyC,UAAU,KAAK;AAC5G;AAHgB;AAKT,SAAS,uCAA6D,OAA+B;AAC3G,QAAM,WAAW,0BAA0B,KAAK;AAChD,SAAO,+BAA+B,oBAAoB,gDAAgD,UAAU,KAAK;AAC1H;AAHgB;AAKT,SAAS,0BAAgD,OAA+B;AAC9F,QAAM,WAAW,2BAA2B,KAAK;AACjD,SAAO,+BAA+B,OAAO,mCAAmC,UAAU,KAAK;AAChG;AAHgB;AAKT,SAAS,6BAAmD,OAA+B;AACjG,QAAM,WAAW,2BAA2B,KAAK;AACjD,SAAO,+BAA+B,UAAU,sCAAsC,UAAU,KAAK;AACtG;AAHgB;AAKT,SAAS,0BAAgD,OAAe,WAAmC;AACjH,QAAM,WAAW,0BAA0B,KAAK,6BAA6B,SAAS;AACtF,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,cAAc,SAAS,MAAM,aAAa,YACpD,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,mCAAmC,mCAAmC,OAAO,QAAQ,CAAC;AAAA,IACjI;AAAA,EACD;AACD;AATgB;AAWT,SAAS,mCAAyD,OAAe,KAAa;AACpG,QAAM,WAAW,0BAA0B,KAAK,8BAA8B,GAAG;AACjF,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,cAAc,SAAS,MAAM,cAAc,MACrD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI,wBAAwB,4CAA4C,mCAAmC,OAAO,QAAQ;AAAA,MAC1H;AAAA,IACJ;AAAA,EACD;AACD;AAXgB;AAaT,SAAS,mCAAyD,YAAoB,WAAmC;AAC/H,QAAM,WAAW,yBAAyB,UAAU,6BAA6B,SAAS;AAC1F,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,aAAa,cAAc,MAAM,aAAa,YACxD,OAAO,GAAG,KAAK,IACf,OAAO;AAAA,QACP,IAAI,wBAAwB,4CAA4C,mCAAmC,OAAO,QAAQ;AAAA,MAC1H;AAAA,IACJ;AAAA,EACD;AACD;AAXgB;AAahB,SAAS,2BACR,YACA,MACA,UACA,QACiB;AACjB,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,WAAW,MAAM,QAAQ,MAAM,IACnC,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,MAAM,8BAA8B,OAAO,QAAQ,CAAC;AAAA,IAC/F;AAAA,EACD;AACD;AAbS;AAeF,SAAS,yBAA+C,OAA+B;AAC7F,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,2BAA2B,UAAU,kCAAkC,UAAU,KAAK;AAC9F;AAHgB;AAKT,SAAS,gCAAsD,OAA+B;AACpG,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,2BAA2B,iBAAiB,yCAAyC,UAAU,KAAK;AAC5G;AAHgB;AAKT,SAAS,4BAAkD,OAA+B;AAChG,QAAM,WAAW,qBAAqB,KAAK;AAC3C,SAAO,2BAA2B,aAAa,qCAAqC,UAAU,KAAK;AACpG;AAHgB;AAKT,SAAS,mCAAyD,OAA+B;AACvG,QAAM,WAAW,sBAAsB,KAAK;AAC5C,SAAO,2BAA2B,oBAAoB,4CAA4C,UAAU,KAAK;AAClH;AAHgB;AAKT,SAAS,sBAA4C,OAA+B;AAC1F,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,2BAA2B,OAAO,+BAA+B,UAAU,KAAK;AACxF;AAHgB;AAKT,SAAS,yBAA+C,OAA+B;AAC7F,QAAM,WAAW,uBAAuB,KAAK;AAC7C,SAAO,2BAA2B,UAAU,kCAAkC,UAAU,KAAK;AAC9F;AAHgB;AAKT,SAAS,sBAA4C,OAAe,WAAmC;AAC7G,QAAM,WAAW,sBAAsB,KAAK,yBAAyB,SAAS;AAC9E,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,UAAU,SAAS,MAAM,SAAS,YAC5C,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,+BAA+B,8BAA8B,OAAO,QAAQ,CAAC;AAAA,IACxH;AAAA,EACD;AACD;AATgB;AAWT,SAAS,+BAAqD,OAAe,KAA6B;AAChH,QAAM,WAAW,sBAAsB,KAAK,0BAA0B,GAAG;AACzE,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,UAAU,SAAS,MAAM,UAAU,MAC7C,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,wCAAwC,8BAA8B,OAAO,QAAQ,CAAC;AAAA,IACjI;AAAA,EACD;AACD;AATgB;AAWT,SAAS,+BAAqD,YAAoB,WAAmC;AAC3H,QAAM,WAAW,qBAAqB,UAAU,yBAAyB,SAAS;AAClF,SAAO;AAAA,IACN,IAAI,OAAU;AACb,aAAO,MAAM,SAAS,cAAc,MAAM,SAAS,YAChD,OAAO,GAAG,KAAK,IACf,OAAO,IAAI,IAAI,wBAAwB,wCAAwC,8BAA8B,OAAO,QAAQ,CAAC;AAAA,IACjI;AAAA,EACD;AACD;AATgB;;;ACtKhB,IAAM,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AAEhC,IAAM,QAAQ,wBAAC,SAAiB;AACtC,SAAO,GAAG,OAAO,SAAS,KAAK,CAAC,EAAE,YAAY,CAAC,IAAI,OAAO,GAAG,IAAI,IAAI;AACtE,GAFqB;;;ACWd,IAAM,cAAc;AAAA,EAC1B,WAAW,CAAC,MAA+B,aAAa;AAAA,EACxD,YAAY,CAAC,MAAgC,aAAa;AAAA,EAC1D,mBAAmB,CAAC,MAAuC,aAAa;AAAA,EACxE,YAAY,CAAC,MAAgC,aAAa;AAAA,EAC1D,aAAa,CAAC,MAAiC,aAAa;AAAA,EAC5D,YAAY,CAAC,MAAgC,aAAa;AAAA,EAC1D,aAAa,CAAC,MAAiC,aAAa;AAAA,EAC5D,cAAc,CAAC,MAAkC,aAAa;AAAA,EAC9D,cAAc,CAAC,MAAkC,aAAa;AAAA,EAC9D,eAAe,CAAC,MAAmC,aAAa;AAAA,EAChE,gBAAgB,CAAC,MAAoC,aAAa;AAAA,EAClE,YAAY,CAAC,MAAgC,YAAY,OAAO,CAAC,KAAK,EAAE,aAAa;AACtF;;;ACCO,IAAM,uBAAN,MAAM,6BAAkD,cAAiB;AAAA,EAGxE,YAAY,MAAsB,cAAyC,CAAC,GAAG;AACrF,UAAM,WAAW;AACjB,SAAK,OAAO;AAAA,EACb;AAAA,EAEO,mBAAmB,QAAgB;AACzC,WAAO,KAAK,cAAc,6BAA6B,MAAM,CAAC;AAAA,EAC/D;AAAA,EAEO,0BAA0B,QAAgB;AAChD,WAAO,KAAK,cAAc,oCAAoC,MAAM,CAAC;AAAA,EACtE;AAAA,EAEO,sBAAsB,QAAgB;AAC5C,WAAO,KAAK,cAAc,gCAAgC,MAAM,CAAC;AAAA,EAClE;AAAA,EAEO,6BAA6B,QAAgB;AACnD,WAAO,KAAK,cAAc,uCAAuC,MAAM,CAAC;AAAA,EACzE;AAAA,EAEO,gBAAgB,QAAgB;AACtC,WAAO,KAAK,cAAc,0BAA0B,MAAM,CAAC;AAAA,EAC5D;AAAA,EAEO,mBAAmB,QAAgB;AACzC,WAAO,KAAK,cAAc,6BAA6B,MAAM,CAAC;AAAA,EAC/D;AAAA,EAEO,gBAAgB,OAAe,WAAmB;AACxD,WAAO,KAAK,cAAc,0BAA0B,OAAO,SAAS,CAAC;AAAA,EACtE;AAAA,EAEO,yBAAyB,SAAiB,OAAe;AAC/D,WAAO,KAAK,cAAc,mCAAmC,SAAS,KAAK,CAAmB;AAAA,EAC/F;AAAA,EAEO,yBAAyB,YAAoB,WAAmB;AACtE,WAAO,KAAK,cAAc,mCAAmC,YAAY,SAAS,CAAC;AAAA,EACpF;AAAA,EAEO,eAAe,QAAgB;AACrC,WAAO,KAAK,cAAc,yBAAyB,MAAM,CAAC;AAAA,EAC3D;AAAA,EAEO,sBAAsB,QAAgB;AAC5C,WAAO,KAAK,cAAc,gCAAgC,MAAM,CAAC;AAAA,EAClE;AAAA,EAEO,kBAAkB,QAAgB;AACxC,WAAO,KAAK,cAAc,4BAA4B,MAAM,CAAC;AAAA,EAC9D;AAAA,EAEO,yBAAyB,QAAgB;AAC/C,WAAO,KAAK,cAAc,mCAAmC,MAAM,CAAC;AAAA,EACrE;AAAA,EAEO,YAAY,QAAgB;AAClC,WAAO,KAAK,cAAc,sBAAsB,MAAM,CAAC;AAAA,EACxD;AAAA,EAEO,eAAe,QAAgB;AACrC,WAAO,KAAK,cAAc,yBAAyB,MAAM,CAAC;AAAA,EAC3D;AAAA,EAEO,YAAY,OAAe,WAAmB;AACpD,WAAO,KAAK,cAAc,sBAAsB,OAAO,SAAS,CAAC;AAAA,EAClE;AAAA,EAEO,qBAAqB,SAAiB,OAAe;AAC3D,WAAO,KAAK,cAAc,+BAA+B,SAAS,KAAK,CAAC;AAAA,EACzE;AAAA,EAEO,qBAAqB,YAAoB,WAAmB;AAClE,WAAO,KAAK,cAAc,+BAA+B,YAAY,SAAS,CAAC;AAAA,EAChF;AAAA,EAEmB,QAAc;AAChC,WAAO,QAAQ,UAAU,KAAK,aAAa,CAAC,KAAK,MAAM,KAAK,WAAW,CAAC;AAAA,EACzE;AAAA,EAEU,OAAO,OAA4C;AAC5D,WAAO,YAAY,KAAK,IAAI,EAAE,KAAK,IAChC,OAAO,GAAG,KAAU,IACpB,OAAO,IAAI,IAAI,gBAAgB,gBAAgB,YAAY,MAAM,KAAK,IAAI,CAAC,IAAI,KAAK,CAAC;AAAA,EACzF;AACD;AAzFgF;AAAzE,IAAM,sBAAN;;;ACAA,IAAM,UAAN,MAAM,QAAO;AAAA,EACnB,IAAW,SAAS;AACnB,WAAO,IAAI,gBAAgB;AAAA,EAC5B;AAAA,EAEA,IAAW,SAAS;AACnB,WAAO,IAAI,gBAAgB;AAAA,EAC5B;AAAA,EAEA,IAAW,SAAS;AACnB,WAAO,IAAI,gBAAgB;AAAA,EAC5B;AAAA,EAEA,IAAW,UAAU;AACpB,WAAO,IAAI,iBAAiB;AAAA,EAC7B;AAAA,EAEA,IAAW,OAAO;AACjB,WAAO,IAAI,cAAc;AAAA,EAC1B;AAAA,EAEO,OAAyB,OAAiC;AAChE,WAAO,IAAI,gBAAmB,KAAK;AAAA,EACpC;AAAA,EAEA,IAAW,YAAY;AACtB,WAAO,KAAK,QAAQ,MAAS;AAAA,EAC9B;AAAA,EAEA,IAAW,OAAO;AACjB,WAAO,KAAK,QAAQ,IAAI;AAAA,EACzB;AAAA,EAEA,IAAW,UAAU;AACpB,WAAO,IAAI,iBAAiB;AAAA,EAC7B;AAAA,EAEA,IAAW,MAAM;AAChB,WAAO,IAAI,qBAA0B;AAAA,EACtC;AAAA,EAEA,IAAW,UAAU;AACpB,WAAO,IAAI,qBAA8B;AAAA,EAC1C;AAAA,EAEA,IAAW,QAAQ;AAClB,WAAO,IAAI,eAAe;AAAA,EAC3B;AAAA,EAEO,QAAW,QAAsB;AACvC,WAAO,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,KAAK,CAAC,CAAC;AAAA,EAChE;AAAA,EAEO,WAAqC,WAAsC;AACjF,WAAO,IAAI,oBAAoB,SAAS;AAAA,EACzC;AAAA,EAEO,QAAW,OAA4B;AAC7C,QAAI,iBAAiB;AAAM,aAAO,KAAK,KAAK,MAAM,KAAK;AACvD,WAAO,IAAI,iBAAiB,KAAK;AAAA,EAClC;AAAA,EAEO,SAAY,UAAgD;AAClE,WAAO,IAAI,kBAAkB,QAAQ;AAAA,EACtC;AAAA,EAEO,SAA8C,YAAuD;AAC3G,WAAO,IAAI,eAAe,UAAU;AAAA,EACrC;AAAA,EAIO,MAA2B,WAAqC;AACtE,WAAO,IAAI,eAAe,SAAS;AAAA,EACpC;AAAA,EAEO,WAAiC,OAAuB,cAAc;AAC5E,WAAO,IAAI,oBAAuB,IAAI;AAAA,EACvC;AAAA,EAEA,IAAW,YAAY;AACtB,WAAO,KAAK,WAAsB,WAAW;AAAA,EAC9C;AAAA,EAEA,IAAW,aAAa;AACvB,WAAO,KAAK,WAAuB,YAAY;AAAA,EAChD;AAAA,EAEA,IAAW,oBAAoB;AAC9B,WAAO,KAAK,WAA8B,mBAAmB;AAAA,EAC9D;AAAA,EAEA,IAAW,aAAa;AACvB,WAAO,KAAK,WAAuB,YAAY;AAAA,EAChD;AAAA,EAEA,IAAW,cAAc;AACxB,WAAO,KAAK,WAAwB,aAAa;AAAA,EAClD;AAAA,EAEA,IAAW,aAAa;AACvB,WAAO,KAAK,WAAuB,YAAY;AAAA,EAChD;AAAA,EAEA,IAAW,cAAc;AACxB,WAAO,KAAK,WAAwB,aAAa;AAAA,EAClD;AAAA,EAEA,IAAW,eAAe;AACzB,WAAO,KAAK,WAAyB,cAAc;AAAA,EACpD;AAAA,EAEA,IAAW,eAAe;AACzB,WAAO,KAAK,WAAyB,cAAc;AAAA,EACpD;AAAA,EAEA,IAAW,gBAAgB;AAC1B,WAAO,KAAK,WAA0B,eAAe;AAAA,EACtD;AAAA,EAEA,IAAW,iBAAiB;AAC3B,WAAO,KAAK,WAA2B,gBAAgB;AAAA,EACxD;AAAA,EAEO,MAA2C,YAAoD;AACrG,WAAO,IAAI,eAAe,UAAU;AAAA,EACrC;AAAA,EAEO,IAAO,WAA6B;AAC1C,WAAO,IAAI,aAAa,SAAS;AAAA,EAClC;AAAA,EAEO,OAAU,WAA6B;AAC7C,WAAO,IAAI,gBAAgB,SAAS;AAAA,EACrC;AAAA,EAEO,IAAU,cAAgC,gBAAkC;AAClF,WAAO,IAAI,aAAa,cAAc,cAAc;AAAA,EACrD;AAAA,EAEO,KAAuC,WAAkC;AAC/E,WAAO,IAAI,cAAc,SAAS;AAAA,EACnC;AACD;AA/IoB;AAAb,IAAM,SAAN;;;ACzBA,IAAM,IAAI,IAAI,OAAO", "sourcesContent": ["let validationEnabled = true;\n\n/**\n * Sets whether validators should run on the input, or if the input should be passed through.\n * @param enabled Whether validation should be done on inputs\n */\nexport function setGlobalValidationEnabled(enabled: boolean) {\n\tvalidationEnabled = enabled;\n}\n\n/**\n * @returns Whether validation is enabled\n */\nexport function getGlobalValidationEnabled() {\n\treturn validationEnabled;\n}\n", "export class Result<T, E extends Error = Error> {\n\tpublic readonly success: boolean;\n\tpublic readonly value?: T;\n\tpublic readonly error?: E;\n\n\tprivate constructor(success: boolean, value?: T, error?: E) {\n\t\tthis.success = success;\n\t\tif (success) {\n\t\t\tthis.value = value;\n\t\t} else {\n\t\t\tthis.error = error;\n\t\t}\n\t}\n\n\tpublic isOk(): this is { success: true; value: T } {\n\t\treturn this.success;\n\t}\n\n\tpublic isErr(): this is { success: false; error: E } {\n\t\treturn !this.success;\n\t}\n\n\tpublic unwrap(): T {\n\t\tif (this.isOk()) return this.value;\n\t\tthrow this.error as Error;\n\t}\n\n\tpublic static ok<T, E extends Error = Error>(value: T): Result<T, E> {\n\t\treturn new Result<T, E>(true, value);\n\t}\n\n\tpublic static err<T, E extends Error = Error>(error: E): Result<T, E> {\n\t\treturn new Result<T, E>(false, undefined, error);\n\t}\n}\n", "// https://github.com/microsoft/TypeScript/issues/37663\ntype Fn = (...args: unknown[]) => unknown;\n\nexport function getValue<T, U = T extends Fn ? ReturnType<T> : T>(valueOrFn: T): U {\n\treturn typeof valueOrFn === 'function' ? valueOrFn() : valueOrFn;\n}\n", "import get from 'lodash/get.js';\nimport { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { BaseValidator } from '../type-exports';\nimport type { IConstraint } from './type-exports';\n\nexport type ObjectConstraintName = `s.object(T.when)`;\n\nexport type WhenKey = PropertyKey | PropertyKey[];\n\nexport interface WhenOptions<T extends BaseValidator<any>, Key extends WhenKey> {\n\tis?: boolean | ((value: Key extends Array<any> ? any[] : any) => boolean);\n\tthen: (predicate: T) => T;\n\totherwise?: (predicate: T) => T;\n}\n\nexport function whenConstraint<T extends BaseValidator<any>, I, Key extends WhenKey>(\n\tkey: Key,\n\toptions: WhenOptions<T, Key>,\n\tvalidator: T\n): IConstraint<I> {\n\treturn {\n\t\trun(input: I, parent?: any) {\n\t\t\tif (!parent) {\n\t\t\t\treturn Result.err(new ExpectedConstraintError('s.object(T.when)', 'Validator has no parent', parent, 'Validator to have a parent'));\n\t\t\t}\n\n\t\t\tconst isKeyArray = Array.isArray(key);\n\n\t\t\tconst value = isKeyArray ? key.map((k) => get(parent, k)) : get(parent, key);\n\n\t\t\tconst predicate = resolveBooleanIs<T, Key>(options, value, isKeyArray) ? options.then : options.otherwise;\n\n\t\t\tif (predicate) {\n\t\t\t\treturn predicate(validator).run(input) as Result<I, ExpectedConstraintError<I>>;\n\t\t\t}\n\n\t\t\treturn Result.ok(input);\n\t\t}\n\t};\n}\n\nfunction resolveBooleanIs<T extends BaseValidator<any>, Key extends WhenKey>(options: WhenOptions<T, Key>, value: any, isKeyArray: boolean) {\n\tif (options.is === undefined) {\n\t\treturn isKeyArray ? !value.some((val: any) => !val) : Boolean(value);\n\t}\n\n\tif (typeof options.is === 'function') {\n\t\treturn options.is(value);\n\t}\n\n\treturn value === options.is;\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { customInspectSymbolStackLess } from './BaseError';\nimport { BaseConstraintError, type ConstraintErrorNames } from './BaseConstraintError';\n\nexport class ExpectedConstraintError<T = unknown> extends BaseConstraintError<T> {\n\tpublic readonly expected: string;\n\n\tpublic constructor(constraint: ConstraintErrorNames, message: string, given: T, expected: string) {\n\t\tsuper(constraint, message, given);\n\t\tthis.expected = expected;\n\t}\n\n\tpublic toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tconstraint: this.constraint,\n\t\t\tgiven: this.given,\n\t\t\texpected: this.expected\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst constraint = options.stylize(this.constraint, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[ExpectedConstraintError: ${constraint}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1 };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('ExpectedConstraintError', 'special')} > ${constraint}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst expectedBlock = `\\n  ${options.stylize('Expected: ', 'string')}${options.stylize(this.expected, 'boolean')}`;\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${expectedBlock}\\n${givenBlock}`;\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\n\nexport const customInspectSymbol = Symbol.for('nodejs.util.inspect.custom');\nexport const customInspectSymbolStackLess = Symbol.for('nodejs.util.inspect.custom.stack-less');\n\nexport abstract class BaseError extends Error {\n\tprotected [customInspectSymbol](depth: number, options: InspectOptionsStylized) {\n\t\treturn `${this[customInspectSymbolStackLess](depth, options)}\\n${this.stack!.slice(this.stack!.indexOf('\\n'))}`;\n\t}\n\n\tprotected abstract [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string;\n}\n", "import type {\n\tArray<PERSON>onstraintName,\n\tBigInt<PERSON>onstraintName,\n\tBooleanConstraintName,\n\tDateConstraintName,\n\tNumberConstraintName,\n\tObjectConstraintName,\n\tStringConstraintName,\n\tTypedArrayConstraintName\n} from '../../constraints/type-exports';\nimport { BaseError } from './BaseError';\n\nexport type ConstraintErrorNames =\n\t| TypedArrayConstraintName\n\t| ArrayConstraintName\n\t| BigIntConstraintName\n\t| BooleanConstraintName\n\t| DateConstraintName\n\t| NumberConstraintName\n\t| ObjectConstraintName\n\t| StringConstraintName;\n\nexport abstract class BaseConstraintError<T = unknown> extends BaseError {\n\tpublic readonly constraint: ConstraintErrorNames;\n\tpublic readonly given: T;\n\n\tpublic constructor(constraint: ConstraintErrorNames, message: string, given: T) {\n\t\tsuper(message);\n\t\tthis.constraint = constraint;\n\t\tthis.given = given;\n\t}\n}\n", "import { getGlobalValidationEnabled } from '../lib/configs';\nimport { Result } from '../lib/Result';\nimport { ArrayValidator, DefaultValidator, LiteralValidator, NullishValidator, SetValidator, UnionValidator } from './imports';\nimport { getValue } from './util/getValue';\nimport { whenConstraint, type WhenKey, type WhenOptions } from '../constraints/ObjectConstrains';\nimport type { CombinedError } from '../lib/errors/CombinedError';\nimport type { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport type { UnknownEnumValueError } from '../lib/errors/UnknownEnumValueError';\nimport type { ValidationError } from '../lib/errors/ValidationError';\nimport type { BaseConstraintError, InferResultType } from '../type-exports';\nimport type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\n\nexport abstract class BaseValidator<T> {\n\tpublic description?: string;\n\tprotected parent?: object;\n\tprotected constraints: readonly IConstraint<T>[] = [];\n\tprotected isValidationEnabled: boolean | (() => boolean) | null = null;\n\n\tpublic constructor(constraints: readonly IConstraint<T>[] = []) {\n\t\tthis.constraints = constraints;\n\t}\n\n\tpublic setParent(parent: object): this {\n\t\tthis.parent = parent;\n\t\treturn this;\n\t}\n\n\tpublic get optional(): UnionValidator<T | undefined> {\n\t\treturn new UnionValidator([new LiteralValidator(undefined), this.clone()]);\n\t}\n\n\tpublic get nullable(): UnionValidator<T | null> {\n\t\treturn new UnionValidator([new LiteralValidator(null), this.clone()]);\n\t}\n\n\tpublic get nullish(): UnionValidator<T | null | undefined> {\n\t\treturn new UnionValidator([new NullishValidator(), this.clone()]);\n\t}\n\n\tpublic get array(): ArrayValidator<T[]> {\n\t\treturn new ArrayValidator<T[]>(this.clone());\n\t}\n\n\tpublic get set(): SetValidator<T> {\n\t\treturn new SetValidator<T>(this.clone());\n\t}\n\n\tpublic or<O>(...predicates: readonly BaseValidator<O>[]): UnionValidator<T | O> {\n\t\treturn new UnionValidator<T | O>([this.clone(), ...predicates]);\n\t}\n\n\tpublic transform(cb: (value: T) => T): this;\n\tpublic transform<O>(cb: (value: T) => O): BaseValidator<O>;\n\tpublic transform<O>(cb: (value: T) => O): BaseValidator<O> {\n\t\treturn this.addConstraint({ run: (input) => Result.ok(cb(input) as unknown as T) }) as unknown as BaseValidator<O>;\n\t}\n\n\tpublic reshape(cb: (input: T) => Result<T>): this;\n\tpublic reshape<R extends Result<unknown>, O = InferResultType<R>>(cb: (input: T) => R): BaseValidator<O>;\n\tpublic reshape<R extends Result<unknown>, O = InferResultType<R>>(cb: (input: T) => R): BaseValidator<O> {\n\t\treturn this.addConstraint({ run: cb as unknown as (input: T) => Result<T, BaseConstraintError<T>> }) as unknown as BaseValidator<O>;\n\t}\n\n\tpublic default(value: Exclude<T, undefined> | (() => Exclude<T, undefined>)): DefaultValidator<Exclude<T, undefined>> {\n\t\treturn new DefaultValidator(this.clone() as unknown as BaseValidator<Exclude<T, undefined>>, value);\n\t}\n\n\tpublic when<Key extends WhenKey, This extends BaseValidator<any> = this>(key: Key, options: WhenOptions<This, Key>): this {\n\t\treturn this.addConstraint(whenConstraint<This, T, Key>(key, options, this as unknown as This));\n\t}\n\n\tpublic describe(description: string): this {\n\t\tconst clone = this.clone();\n\t\tclone.description = description;\n\t\treturn clone;\n\t}\n\n\tpublic run(value: unknown): Result<T, BaseError> {\n\t\tlet result = this.handle(value) as Result<T, BaseError>;\n\t\tif (result.isErr()) return result;\n\n\t\tfor (const constraint of this.constraints) {\n\t\t\tresult = constraint.run(result.value as T, this.parent);\n\t\t\tif (result.isErr()) break;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tpublic parse<R extends T = T>(value: unknown): R {\n\t\t// If validation is disabled (at the validator or global level), we only run the `handle` method, which will do some basic checks\n\t\t// (like that the input is a string for a string validator)\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn this.handle(value).unwrap() as R;\n\t\t}\n\n\t\treturn this.constraints.reduce((v, constraint) => constraint.run(v).unwrap(), this.handle(value).unwrap()) as R;\n\t}\n\n\tpublic is<R extends T = T>(value: unknown): value is R {\n\t\treturn this.run(value).isOk();\n\t}\n\n\t/**\n\t * Sets if the validator should also run constraints or just do basic checks.\n\t * @param isValidationEnabled Whether this validator should be enabled or disabled. You can pass boolean or a function returning boolean which will be called just before parsing.\n\t * Set to `null` to go off of the global configuration.\n\t */\n\tpublic setValidationEnabled(isValidationEnabled: boolean | (() => boolean) | null): this {\n\t\tconst clone = this.clone();\n\t\tclone.isValidationEnabled = isValidationEnabled;\n\t\treturn clone;\n\t}\n\n\tpublic getValidationEnabled() {\n\t\treturn getValue(this.isValidationEnabled);\n\t}\n\n\tprotected get shouldRunConstraints(): boolean {\n\t\treturn getValue(this.isValidationEnabled) ?? getGlobalValidationEnabled();\n\t}\n\n\tprotected clone(): this {\n\t\tconst clone: this = Reflect.construct(this.constructor, [this.constraints]);\n\t\tclone.isValidationEnabled = this.isValidationEnabled;\n\t\treturn clone;\n\t}\n\n\tprotected abstract handle(value: unknown): Result<T, ValidatorError>;\n\n\tprotected addConstraint(constraint: IConstraint<T>): this {\n\t\tconst clone = this.clone();\n\t\tclone.constraints = clone.constraints.concat(constraint);\n\t\treturn clone;\n\t}\n}\n\nexport type ValidatorError = ValidationError | CombinedError | CombinedPropertyError | UnknownEnumValueError;\n", "import fastDeepEqual from 'fast-deep-equal/es6/index.js';\nimport uniqWith from 'lodash/uniqWith.js';\n\nexport function isUnique(input: unknown[]) {\n\tif (input.length < 2) return true;\n\tconst uniqueArray = uniqWith(input, fastDeepEqual);\n\treturn uniqueArray.length === input.length;\n}\n", "export function lessThan(a: number, b: number): boolean;\nexport function lessThan(a: bigint, b: bigint): boolean;\nexport function lessThan(a: number | bigint, b: number | bigint): boolean {\n\treturn a < b;\n}\n\nexport function lessThanOrEqual(a: number, b: number): boolean;\nexport function lessThanOrEqual(a: bigint, b: bigint): boolean;\nexport function lessThanOrEqual(a: number | bigint, b: number | bigint): boolean {\n\treturn a <= b;\n}\n\nexport function greaterThan(a: number, b: number): boolean;\nexport function greaterThan(a: bigint, b: bigint): boolean;\nexport function greaterThan(a: number | bigint, b: number | bigint): boolean {\n\treturn a > b;\n}\n\nexport function greaterThanOrEqual(a: number, b: number): boolean;\nexport function greaterThanOrEqual(a: bigint, b: bigint): boolean;\nexport function greaterThanOrEqual(a: number | bigint, b: number | bigint): boolean {\n\treturn a >= b;\n}\n\nexport function equal(a: number, b: number): boolean;\nexport function equal(a: bigint, b: bigint): boolean;\nexport function equal(a: number | bigint, b: number | bigint): boolean {\n\treturn a === b;\n}\n\nexport function notEqual(a: number, b: number): boolean;\nexport function notEqual(a: bigint, b: bigint): boolean;\nexport function notEqual(a: number | bigint, b: number | bigint): boolean {\n\treturn a !== b;\n}\n\nexport interface Comparator {\n\t(a: number, b: number): boolean;\n\t(a: bigint, b: bigint): boolean;\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\nimport { isUnique } from './util/isUnique';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type ArrayConstraintName = `s.array(T).${\n\t| 'unique'\n\t| `length${\n\t\t\t| 'LessThan'\n\t\t\t| 'LessThanOrEqual'\n\t\t\t| 'GreaterThan'\n\t\t\t| 'GreaterThanOrEqual'\n\t\t\t| 'Equal'\n\t\t\t| 'NotEqual'\n\t\t\t| 'Range'\n\t\t\t| 'RangeInclusive'\n\t\t\t| 'RangeExclusive'}`}`;\n\nfunction arrayLengthComparator<T>(comparator: Comparator, name: ArrayConstraintName, expected: string, length: number): IConstraint<T[]> {\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn comparator(input.length, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function arrayLengthLessThan<T>(value: number): IConstraint<T[]> {\n\tconst expected = `expected.length < ${value}`;\n\treturn arrayLengthComparator(lessThan, 's.array(T).lengthLessThan', expected, value);\n}\n\nexport function arrayLengthLessThanOrEqual<T>(value: number): IConstraint<T[]> {\n\tconst expected = `expected.length <= ${value}`;\n\treturn arrayLengthComparator(lessThanOrEqual, 's.array(T).lengthLessThanOrEqual', expected, value);\n}\n\nexport function arrayLengthGreaterThan<T>(value: number): IConstraint<T[]> {\n\tconst expected = `expected.length > ${value}`;\n\treturn arrayLengthComparator(greaterThan, 's.array(T).lengthGreaterThan', expected, value);\n}\n\nexport function arrayLengthGreaterThanOrEqual<T>(value: number): IConstraint<T[]> {\n\tconst expected = `expected.length >= ${value}`;\n\treturn arrayLengthComparator(greaterThanOrEqual, 's.array(T).lengthGreaterThanOrEqual', expected, value);\n}\n\nexport function arrayLengthEqual<T>(value: number): IConstraint<T[]> {\n\tconst expected = `expected.length === ${value}`;\n\treturn arrayLengthComparator(equal, 's.array(T).lengthEqual', expected, value);\n}\n\nexport function arrayLengthNotEqual<T>(value: number): IConstraint<T[]> {\n\tconst expected = `expected.length !== ${value}`;\n\treturn arrayLengthComparator(notEqual, 's.array(T).lengthNotEqual', expected, value);\n}\n\nexport function arrayLengthRange<T>(start: number, endBefore: number): IConstraint<T[]> {\n\tconst expected = `expected.length >= ${start} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn input.length >= start && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.array(T).lengthRange', 'Invalid Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function arrayLengthRangeInclusive<T>(start: number, end: number): IConstraint<T[]> {\n\tconst expected = `expected.length >= ${start} && expected.length <= ${end}`;\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn input.length >= start && input.length <= end //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.array(T).lengthRangeInclusive', 'Invalid Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function arrayLengthRangeExclusive<T>(startAfter: number, endBefore: number): IConstraint<T[]> {\n\tconst expected = `expected.length > ${startAfter} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T[]) {\n\t\t\treturn input.length > startAfter && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.array(T).lengthRangeExclusive', 'Invalid Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport const uniqueArray: IConstraint<unknown[]> = {\n\trun(input: unknown[]) {\n\t\treturn isUnique(input) //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(new ExpectedConstraintError('s.array(T).unique', 'Array values are not unique', input, 'Expected all values to be unique'));\n\t}\n};\n", "import type { InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class CombinedPropertyError extends BaseError {\n\tpublic readonly errors: [<PERSON>Key, BaseError][];\n\n\tpublic constructor(errors: [<PERSON><PERSON><PERSON>, BaseError][]) {\n\t\tsuper('Received one or more errors');\n\n\t\tthis.errors = errors;\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize('[CombinedPropertyError]', 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\n\t\tconst header = `${options.stylize('CombinedPropertyError', 'special')} (${options.stylize(this.errors.length.toString(), 'number')})`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst errors = this.errors\n\t\t\t.map(([key, error]) => {\n\t\t\t\tconst property = CombinedPropertyError.formatProperty(key, options);\n\t\t\t\tconst body = error[customInspectSymbolStackLess](depth - 1, newOptions).replace(/\\n/g, padding);\n\n\t\t\t\treturn `  input${property}${padding}${body}`;\n\t\t\t})\n\t\t\t.join('\\n\\n');\n\t\treturn `${header}\\n  ${message}\\n\\n${errors}`;\n\t}\n\n\tprivate static formatProperty(key: PropertyKey, options: InspectOptionsStylized): string {\n\t\tif (typeof key === 'string') return options.stylize(`.${key}`, 'symbol');\n\t\tif (typeof key === 'number') return `[${options.stylize(key.toString(), 'number')}]`;\n\t\treturn `[${options.stylize('Symbol', 'symbol')}(${key.description})]`;\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class ValidationError extends BaseError {\n\tpublic readonly validator: string;\n\tpublic readonly given: unknown;\n\n\tpublic constructor(validator: string, message: string, given: unknown) {\n\t\tsuper(message);\n\n\t\tthis.validator = validator;\n\t\tthis.given = given;\n\t}\n\n\tpublic toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tvalidator: this.validator,\n\t\t\tgiven: this.given\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst validator = options.stylize(this.validator, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[ValidationError: ${validator}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('ValidationError', 'special')} > ${validator}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${givenBlock}`;\n\t}\n}\n", "import {\n\tarrayLengthEqual,\n\tarrayLengthGreater<PERSON>han,\n\tarrayLengthGreaterThanOrEqual,\n\tarrayLengthLessThan,\n\tarrayLengthLessThanOrEqual,\n\tarrayLengthNotEqual,\n\tarrayLengthRange,\n\tarrayLengthRangeExclusive,\n\tarrayLengthRangeInclusive,\n\tuniqueArray\n} from '../constraints/ArrayConstraints';\nimport type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { ExpandSmallerTuples, Tuple, UnshiftTuple } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class ArrayValidator<T extends unknown[], I = T[number]> extends BaseValidator<T> {\n\tprivate readonly validator: BaseValidator<I>;\n\n\tpublic constructor(validator: BaseValidator<I>, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tpublic lengthLessThan<N extends number>(length: N): ArrayValidator<ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, N>]>>> {\n\t\treturn this.addConstraint(arrayLengthLessThan(length) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthLessThanOrEqual<N extends number>(length: N): ArrayValidator<ExpandSmallerTuples<[...Tuple<I, N>]>> {\n\t\treturn this.addConstraint(arrayLengthLessThanOrEqual(length) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthGreaterThan<N extends number>(length: N): ArrayValidator<[...Tuple<I, N>, I, ...T]> {\n\t\treturn this.addConstraint(arrayLengthGreaterThan(length) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthGreaterThanOrEqual<N extends number>(length: N): ArrayValidator<[...Tuple<I, N>, ...T]> {\n\t\treturn this.addConstraint(arrayLengthGreaterThanOrEqual(length) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthEqual<N extends number>(length: N): ArrayValidator<[...Tuple<I, N>]> {\n\t\treturn this.addConstraint(arrayLengthEqual(length) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthNotEqual(length: number): ArrayValidator<[...T]> {\n\t\treturn this.addConstraint(arrayLengthNotEqual(length) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthRange<S extends number, E extends number>(\n\t\tstart: S,\n\t\tendBefore: E\n\t): ArrayValidator<Exclude<ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, E>]>>, ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, S>]>>>> {\n\t\treturn this.addConstraint(arrayLengthRange(start, endBefore) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthRangeInclusive<S extends number, E extends number>(\n\t\tstartAt: S,\n\t\tendAt: E\n\t): ArrayValidator<Exclude<ExpandSmallerTuples<[...Tuple<I, E>]>, ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, S>]>>>> {\n\t\treturn this.addConstraint(arrayLengthRangeInclusive(startAt, endAt) as IConstraint<T>) as any;\n\t}\n\n\tpublic lengthRangeExclusive<S extends number, E extends number>(\n\t\tstartAfter: S,\n\t\tendBefore: E\n\t): ArrayValidator<Exclude<ExpandSmallerTuples<UnshiftTuple<[...Tuple<I, E>]>>, ExpandSmallerTuples<[...Tuple<T, S>]>>> {\n\t\treturn this.addConstraint(arrayLengthRangeExclusive(startAfter, endBefore) as IConstraint<T>) as any;\n\t}\n\n\tpublic get unique(): this {\n\t\treturn this.addConstraint(uniqueArray as IConstraint<T>);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<T, ValidationError | CombinedPropertyError> {\n\t\tif (!Array.isArray(values)) {\n\t\t\treturn Result.err(new ValidationError('s.array(T)', 'Expected an array', values));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(values as T);\n\t\t}\n\n\t\tconst errors: [number, BaseError][] = [];\n\t\tconst transformed: T = [] as unknown as T;\n\n\t\tfor (let i = 0; i < values.length; i++) {\n\t\t\tconst result = this.validator.run(values[i]);\n\t\t\tif (result.isOk()) transformed.push(result.value);\n\t\t\telse errors.push([i, result.error!]);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type BigIntConstraintName = `s.bigint.${\n\t| 'lessThan'\n\t| 'lessThanOrEqual'\n\t| 'greaterThan'\n\t| 'greaterThanOrEqual'\n\t| 'equal'\n\t| 'notEqual'\n\t| 'divisibleBy'}`;\n\nfunction bigintComparator(comparator: Comparator, name: BigIntConstraintName, expected: string, number: bigint): IConstraint<bigint> {\n\treturn {\n\t\trun(input: bigint) {\n\t\t\treturn comparator(input, number) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid bigint value', input, expected));\n\t\t}\n\t};\n}\n\nexport function bigintLessThan(value: bigint): IConstraint<bigint> {\n\tconst expected = `expected < ${value}n`;\n\treturn bigintComparator(lessThan, 's.bigint.lessThan', expected, value);\n}\n\nexport function bigintLessThanOrEqual(value: bigint): IConstraint<bigint> {\n\tconst expected = `expected <= ${value}n`;\n\treturn bigintComparator(lessThanOrEqual, 's.bigint.lessThanOrEqual', expected, value);\n}\n\nexport function bigintGreaterThan(value: bigint): IConstraint<bigint> {\n\tconst expected = `expected > ${value}n`;\n\treturn bigintComparator(greaterThan, 's.bigint.greaterThan', expected, value);\n}\n\nexport function bigintGreaterThanOrEqual(value: bigint): IConstraint<bigint> {\n\tconst expected = `expected >= ${value}n`;\n\treturn bigintComparator(greaterThanOrEqual, 's.bigint.greaterThanOrEqual', expected, value);\n}\n\nexport function bigintEqual(value: bigint): IConstraint<bigint> {\n\tconst expected = `expected === ${value}n`;\n\treturn bigintComparator(equal, 's.bigint.equal', expected, value);\n}\n\nexport function bigintNotEqual(value: bigint): IConstraint<bigint> {\n\tconst expected = `expected !== ${value}n`;\n\treturn bigintComparator(notEqual, 's.bigint.notEqual', expected, value);\n}\n\nexport function bigintDivisibleBy(divider: bigint): IConstraint<bigint> {\n\tconst expected = `expected % ${divider}n === 0n`;\n\treturn {\n\t\trun(input: bigint) {\n\t\t\treturn input % divider === 0n //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.bigint.divisibleBy', 'BigInt is not divisible', input, expected));\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\tbigintDivisibleBy,\n\tbigintEqual,\n\tbigintGreaterThan,\n\tbigintGreaterThanOrEqual,\n\tbigintLessThan,\n\tbigintLessThanOrEqual,\n\tbigintNotEqual\n} from '../constraints/BigIntConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class BigIntValidator<T extends bigint> extends BaseValidator<T> {\n\tpublic lessThan(number: bigint): this {\n\t\treturn this.addConstraint(bigintLessThan(number) as IConstraint<T>);\n\t}\n\n\tpublic lessThanOrEqual(number: bigint): this {\n\t\treturn this.addConstraint(bigintLessThanOrEqual(number) as IConstraint<T>);\n\t}\n\n\tpublic greaterThan(number: bigint): this {\n\t\treturn this.addConstraint(bigintGreaterThan(number) as IConstraint<T>);\n\t}\n\n\tpublic greaterThanOrEqual(number: bigint): this {\n\t\treturn this.addConstraint(bigintGreaterThanOrEqual(number) as IConstraint<T>);\n\t}\n\n\tpublic equal<N extends bigint>(number: N): BigIntValidator<N> {\n\t\treturn this.addConstraint(bigintEqual(number) as IConstraint<T>) as unknown as BigIntValidator<N>;\n\t}\n\n\tpublic notEqual(number: bigint): this {\n\t\treturn this.addConstraint(bigintNotEqual(number) as IConstraint<T>);\n\t}\n\n\tpublic get positive(): this {\n\t\treturn this.greaterThanOrEqual(0n);\n\t}\n\n\tpublic get negative(): this {\n\t\treturn this.lessThan(0n);\n\t}\n\n\tpublic divisibleBy(number: bigint): this {\n\t\treturn this.addConstraint(bigintDivisibleBy(number) as IConstraint<T>);\n\t}\n\n\tpublic get abs(): this {\n\t\treturn this.transform((value) => (value < 0 ? -value : value) as T);\n\t}\n\n\tpublic intN(bits: number): this {\n\t\treturn this.transform((value) => BigInt.asIntN(bits, value) as T);\n\t}\n\n\tpublic uintN(bits: number): this {\n\t\treturn this.transform((value) => BigInt.asUintN(bits, value) as T);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'bigint' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.bigint', 'Expected a bigint primitive', value));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\n\nexport type BooleanConstraintName = `s.boolean.${boolean}`;\n\nexport const booleanTrue: IConstraint<boolean, true> = {\n\trun(input: boolean) {\n\t\treturn input //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(new ExpectedConstraintError('s.boolean.true', 'Invalid boolean value', input, 'true'));\n\t}\n};\n\nexport const booleanFalse: IConstraint<boolean, false> = {\n\trun(input: boolean) {\n\t\treturn input //\n\t\t\t? Result.err(new ExpectedConstraintError('s.boolean.false', 'Invalid boolean value', input, 'false'))\n\t\t\t: Result.ok(input);\n\t}\n};\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { booleanFalse, booleanTrue } from '../constraints/BooleanConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class BooleanValidator<T extends boolean = boolean> extends BaseValidator<T> {\n\tpublic get true(): BooleanValidator<true> {\n\t\treturn this.addConstraint(booleanTrue as IConstraint<T>) as BooleanValidator<true>;\n\t}\n\n\tpublic get false(): BooleanValidator<false> {\n\t\treturn this.addConstraint(booleanFalse as IConstraint<T>) as BooleanValidator<false>;\n\t}\n\n\tpublic equal<R extends true | false>(value: R): BooleanValidator<R> {\n\t\treturn (value ? this.true : this.false) as BooleanValidator<R>;\n\t}\n\n\tpublic notEqual<R extends true | false>(value: R): BooleanValidator<R> {\n\t\treturn (value ? this.false : this.true) as BooleanValidator<R>;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'boolean' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.boolean', 'Expected a boolean primitive', value));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type DateConstraintName = `s.date.${\n\t| 'lessThan'\n\t| 'lessThanOrEqual'\n\t| 'greaterThan'\n\t| 'greaterThanOrEqual'\n\t| 'equal'\n\t| 'notEqual'\n\t| 'valid'\n\t| 'invalid'}`;\n\nfunction dateComparator(comparator: Comparator, name: DateConstraintName, expected: string, number: number): IConstraint<Date> {\n\treturn {\n\t\trun(input: Date) {\n\t\t\treturn comparator(input.getTime(), number) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid Date value', input, expected));\n\t\t}\n\t};\n}\n\nexport function dateLessThan(value: Date): IConstraint<Date> {\n\tconst expected = `expected < ${value.toISOString()}`;\n\treturn dateComparator(lessThan, 's.date.lessThan', expected, value.getTime());\n}\n\nexport function dateLessThanOrEqual(value: Date): IConstraint<Date> {\n\tconst expected = `expected <= ${value.toISOString()}`;\n\treturn dateComparator(lessThanOrEqual, 's.date.lessThanOrEqual', expected, value.getTime());\n}\n\nexport function dateGreaterThan(value: Date): IConstraint<Date> {\n\tconst expected = `expected > ${value.toISOString()}`;\n\treturn dateComparator(greaterThan, 's.date.greaterThan', expected, value.getTime());\n}\n\nexport function dateGreaterThanOrEqual(value: Date): IConstraint<Date> {\n\tconst expected = `expected >= ${value.toISOString()}`;\n\treturn dateComparator(greaterThanOrEqual, 's.date.greaterThanOrEqual', expected, value.getTime());\n}\n\nexport function dateEqual(value: Date): IConstraint<Date> {\n\tconst expected = `expected === ${value.toISOString()}`;\n\treturn dateComparator(equal, 's.date.equal', expected, value.getTime());\n}\n\nexport function dateNotEqual(value: Date): IConstraint<Date> {\n\tconst expected = `expected !== ${value.toISOString()}`;\n\treturn dateComparator(notEqual, 's.date.notEqual', expected, value.getTime());\n}\n\nexport const dateInvalid: IConstraint<Date> = {\n\trun(input: Date) {\n\t\treturn Number.isNaN(input.getTime()) //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(new ExpectedConstraintError('s.date.invalid', 'Invalid Date value', input, 'expected === NaN'));\n\t}\n};\n\nexport const dateValid: IConstraint<Date> = {\n\trun(input: Date) {\n\t\treturn Number.isNaN(input.getTime()) //\n\t\t\t? Result.err(new ExpectedConstraintError('s.date.valid', 'Invalid Date value', input, 'expected !== NaN'))\n\t\t\t: Result.ok(input);\n\t}\n};\n", "import {\n\tdateEqual,\n\tdateGreater<PERSON>han,\n\tdateGreaterThanOrEqual,\n\tdateInvalid,\n\tdateLessThan,\n\tdateLessThanOrEqual,\n\tdateNotEqual,\n\tdateValid\n} from '../constraints/DateConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class DateValidator extends BaseValidator<Date> {\n\tpublic lessThan(date: Date | number | string): this {\n\t\treturn this.addConstraint(dateLessThan(new Date(date)));\n\t}\n\n\tpublic lessThanOrEqual(date: Date | number | string): this {\n\t\treturn this.addConstraint(dateLessThanOrEqual(new Date(date)));\n\t}\n\n\tpublic greaterThan(date: Date | number | string): this {\n\t\treturn this.addConstraint(dateGreaterThan(new Date(date)));\n\t}\n\n\tpublic greaterThanOrEqual(date: Date | number | string): this {\n\t\treturn this.addConstraint(dateGreaterThanOrEqual(new Date(date)));\n\t}\n\n\tpublic equal(date: Date | number | string): this {\n\t\tconst resolved = new Date(date);\n\t\treturn Number.isNaN(resolved.getTime()) //\n\t\t\t? this.invalid\n\t\t\t: this.addConstraint(dateEqual(resolved));\n\t}\n\n\tpublic notEqual(date: Date | number | string): this {\n\t\tconst resolved = new Date(date);\n\t\treturn Number.isNaN(resolved.getTime()) //\n\t\t\t? this.valid\n\t\t\t: this.addConstraint(dateNotEqual(resolved));\n\t}\n\n\tpublic get valid(): this {\n\t\treturn this.addConstraint(dateValid);\n\t}\n\n\tpublic get invalid(): this {\n\t\treturn this.addConstraint(dateInvalid);\n\t}\n\n\tprotected handle(value: unknown): Result<Date, ValidationError> {\n\t\treturn value instanceof Date //\n\t\t\t? Result.ok(value)\n\t\t\t: Result.err(new ValidationError('s.date', 'Expected a Date', value));\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { customInspectSymbolStackLess } from './BaseError';\nimport { ValidationError } from './ValidationError';\n\nexport class ExpectedValidationError<T> extends ValidationError {\n\tpublic readonly expected: T;\n\n\tpublic constructor(validator: string, message: string, given: unknown, expected: T) {\n\t\tsuper(validator, message, given);\n\t\tthis.expected = expected;\n\t}\n\n\tpublic override toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tvalidator: this.validator,\n\t\t\tgiven: this.given,\n\t\t\texpected: this.expected\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst validator = options.stylize(this.validator, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[ExpectedValidationError: ${validator}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1 };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst expected = inspect(this.expected, newOptions).replace(/\\n/g, padding);\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('ExpectedValidationError', 'special')} > ${validator}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst expectedBlock = `\\n  ${options.stylize('Expected:', 'string')}${padding}${expected}`;\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${expectedBlock}\\n${givenBlock}`;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { ExpectedValidationError } from '../lib/errors/ExpectedValidationError';\nimport { Result } from '../lib/Result';\nimport type { Constructor } from '../lib/util-types';\nimport { BaseValidator } from './imports';\n\nexport class InstanceValidator<T> extends BaseValidator<T> {\n\tpublic readonly expected: Constructor<T>;\n\n\tpublic constructor(expected: Constructor<T>, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.expected = expected;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ExpectedValidationError<Constructor<T>>> {\n\t\treturn value instanceof this.expected //\n\t\t\t? Result.ok(value)\n\t\t\t: Result.err(new ExpectedValidationError('s.instance(V)', 'Expected', value, this.expected));\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.expected, this.constraints]);\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { ExpectedValidationError } from '../lib/errors/ExpectedValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class LiteralValidator<T> extends BaseValidator<T> {\n\tpublic readonly expected: T;\n\n\tpublic constructor(literal: T, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.expected = literal;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ExpectedValidationError<T>> {\n\t\treturn Object.is(value, this.expected) //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ExpectedValidationError('s.literal(V)', 'Expected values to be equals', value, this.expected));\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.expected, this.constraints]);\n\t}\n}\n", "import { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class NeverValidator extends BaseValidator<never> {\n\tprotected handle(value: unknown): Result<never, ValidationError> {\n\t\treturn Result.err(new ValidationError('s.never', 'Expected a value to not be passed', value));\n\t}\n}\n", "import { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class NullishValidator extends BaseValidator<undefined | null> {\n\tprotected handle(value: unknown): Result<undefined | null, ValidationError> {\n\t\treturn value === undefined || value === null //\n\t\t\t? Result.ok(value)\n\t\t\t: Result.err(new ValidationError('s.nullish', 'Expected undefined or null', value));\n\t}\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\n\nexport type NumberConstraintName = `s.number.${\n\t| 'lessThan'\n\t| 'lessThanOrEqual'\n\t| 'greaterThan'\n\t| 'greaterThanOrEqual'\n\t| 'equal'\n\t| 'equal(NaN)'\n\t| 'notEqual'\n\t| 'notEqual(NaN)'\n\t| 'int'\n\t| 'safeInt'\n\t| 'finite'\n\t| 'divisibleBy'}`;\n\nfunction numberComparator(comparator: Comparator, name: NumberConstraintName, expected: string, number: number): IConstraint<number> {\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn comparator(input, number) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid number value', input, expected));\n\t\t}\n\t};\n}\n\nexport function numberLessThan(value: number): IConstraint<number> {\n\tconst expected = `expected < ${value}`;\n\treturn numberComparator(lessThan, 's.number.lessThan', expected, value);\n}\n\nexport function numberLessThanOrEqual(value: number): IConstraint<number> {\n\tconst expected = `expected <= ${value}`;\n\treturn numberComparator(lessThanOrEqual, 's.number.lessThanOrEqual', expected, value);\n}\n\nexport function numberGreaterThan(value: number): IConstraint<number> {\n\tconst expected = `expected > ${value}`;\n\treturn numberComparator(greaterThan, 's.number.greaterThan', expected, value);\n}\n\nexport function numberGreaterThanOrEqual(value: number): IConstraint<number> {\n\tconst expected = `expected >= ${value}`;\n\treturn numberComparator(greaterThanOrEqual, 's.number.greaterThanOrEqual', expected, value);\n}\n\nexport function numberEqual(value: number): IConstraint<number> {\n\tconst expected = `expected === ${value}`;\n\treturn numberComparator(equal, 's.number.equal', expected, value);\n}\n\nexport function numberNotEqual(value: number): IConstraint<number> {\n\tconst expected = `expected !== ${value}`;\n\treturn numberComparator(notEqual, 's.number.notEqual', expected, value);\n}\n\nexport const numberInt: IConstraint<number> = {\n\trun(input: number) {\n\t\treturn Number.isInteger(input) //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(\n\t\t\t\t\tnew ExpectedConstraintError('s.number.int', 'Given value is not an integer', input, 'Number.isInteger(expected) to be true')\n\t\t\t  );\n\t}\n};\n\nexport const numberSafeInt: IConstraint<number> = {\n\trun(input: number) {\n\t\treturn Number.isSafeInteger(input) //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(\n\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t's.number.safeInt',\n\t\t\t\t\t\t'Given value is not a safe integer',\n\t\t\t\t\t\tinput,\n\t\t\t\t\t\t'Number.isSafeInteger(expected) to be true'\n\t\t\t\t\t)\n\t\t\t  );\n\t}\n};\n\nexport const numberFinite: IConstraint<number> = {\n\trun(input: number) {\n\t\treturn Number.isFinite(input) //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(new ExpectedConstraintError('s.number.finite', 'Given value is not finite', input, 'Number.isFinite(expected) to be true'));\n\t}\n};\n\nexport const numberNaN: IConstraint<number> = {\n\trun(input: number) {\n\t\treturn Number.isNaN(input) //\n\t\t\t? Result.ok(input)\n\t\t\t: Result.err(new ExpectedConstraintError('s.number.equal(NaN)', 'Invalid number value', input, 'expected === NaN'));\n\t}\n};\n\nexport const numberNotNaN: IConstraint<number> = {\n\trun(input: number) {\n\t\treturn Number.isNaN(input) //\n\t\t\t? Result.err(new ExpectedConstraintError('s.number.notEqual(NaN)', 'Invalid number value', input, 'expected !== NaN'))\n\t\t\t: Result.ok(input);\n\t}\n};\n\nexport function numberDivisibleBy(divider: number): IConstraint<number> {\n\tconst expected = `expected % ${divider} === 0`;\n\treturn {\n\t\trun(input: number) {\n\t\t\treturn input % divider === 0 //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.number.divisibleBy', 'Number is not divisible', input, expected));\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\tnumberDivisibleBy,\n\tnumberEqual,\n\tnumberFinite,\n\tnumberGreaterThan,\n\tnumberGreaterThanOrEqual,\n\tnumberInt,\n\tnumberLessThan,\n\tnumberLessThanOrEqual,\n\tnumberNaN,\n\tnumberNotEqual,\n\tnumberNotNaN,\n\tnumberSafeInt\n} from '../constraints/NumberConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class NumberValidator<T extends number> extends BaseValidator<T> {\n\tpublic lessThan(number: number): this {\n\t\treturn this.addConstraint(numberLessThan(number) as IConstraint<T>);\n\t}\n\n\tpublic lessThanOrEqual(number: number): this {\n\t\treturn this.addConstraint(numberLessThanOrEqual(number) as IConstraint<T>);\n\t}\n\n\tpublic greaterThan(number: number): this {\n\t\treturn this.addConstraint(numberGreaterThan(number) as IConstraint<T>);\n\t}\n\n\tpublic greaterThanOrEqual(number: number): this {\n\t\treturn this.addConstraint(numberGreaterThanOrEqual(number) as IConstraint<T>);\n\t}\n\n\tpublic equal<N extends number>(number: N): NumberValidator<N> {\n\t\treturn Number.isNaN(number) //\n\t\t\t? (this.addConstraint(numberNaN as IConstraint<T>) as unknown as NumberValidator<N>)\n\t\t\t: (this.addConstraint(numberEqual(number) as IConstraint<T>) as unknown as NumberValidator<N>);\n\t}\n\n\tpublic notEqual(number: number): this {\n\t\treturn Number.isNaN(number) //\n\t\t\t? this.addConstraint(numberNotNaN as IConstraint<T>)\n\t\t\t: this.addConstraint(numberNotEqual(number) as IConstraint<T>);\n\t}\n\n\tpublic get int(): this {\n\t\treturn this.addConstraint(numberInt as IConstraint<T>);\n\t}\n\n\tpublic get safeInt(): this {\n\t\treturn this.addConstraint(numberSafeInt as IConstraint<T>);\n\t}\n\n\tpublic get finite(): this {\n\t\treturn this.addConstraint(numberFinite as IConstraint<T>);\n\t}\n\n\tpublic get positive(): this {\n\t\treturn this.greaterThanOrEqual(0);\n\t}\n\n\tpublic get negative(): this {\n\t\treturn this.lessThan(0);\n\t}\n\n\tpublic divisibleBy(divider: number): this {\n\t\treturn this.addConstraint(numberDivisibleBy(divider) as IConstraint<T>);\n\t}\n\n\tpublic get abs(): this {\n\t\treturn this.transform(Math.abs as (value: number) => T);\n\t}\n\n\tpublic get sign(): this {\n\t\treturn this.transform(Math.sign as (value: number) => T);\n\t}\n\n\tpublic get trunc(): this {\n\t\treturn this.transform(Math.trunc as (value: number) => T);\n\t}\n\n\tpublic get floor(): this {\n\t\treturn this.transform(Math.floor as (value: number) => T);\n\t}\n\n\tpublic get fround(): this {\n\t\treturn this.transform(Math.fround as (value: number) => T);\n\t}\n\n\tpublic get round(): this {\n\t\treturn this.transform(Math.round as (value: number) => T);\n\t}\n\n\tpublic get ceil(): this {\n\t\treturn this.transform(Math.ceil as (value: number) => T);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'number' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.number', 'Expected a number primitive', value));\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class MissingPropertyError extends BaseError {\n\tpublic readonly property: PropertyKey;\n\n\tpublic constructor(property: PropertyKey) {\n\t\tsuper('A required property is missing');\n\t\tthis.property = property;\n\t}\n\n\tpublic toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tproperty: this.property\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst property = options.stylize(this.property.toString(), 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[MissingPropertyError: ${property}]`, 'special');\n\t\t}\n\n\t\tconst header = `${options.stylize('MissingPropertyError', 'special')} > ${property}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\treturn `${header}\\n  ${message}`;\n\t}\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class UnknownPropertyError extends BaseError {\n\tpublic readonly property: PropertyKey;\n\tpublic readonly value: unknown;\n\n\tpublic constructor(property: PropertyKey, value: unknown) {\n\t\tsuper('Received unexpected property');\n\n\t\tthis.property = property;\n\t\tthis.value = value;\n\t}\n\n\tpublic toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tproperty: this.property,\n\t\t\tvalue: this.value\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst property = options.stylize(this.property.toString(), 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[UnknownPropertyError: ${property}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst given = inspect(this.value, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('UnknownPropertyError', 'special')} > ${property}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${givenBlock}`;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport { Result } from '../lib/Result';\nimport type { ValidatorError } from './BaseValidator';\nimport { BaseValidator } from './imports';\nimport { getValue } from './util/getValue';\n\nexport class DefaultValidator<T> extends BaseValidator<T> {\n\tprivate readonly validator: BaseValidator<T>;\n\tprivate defaultValue: T | (() => T);\n\n\tpublic constructor(validator: BaseValidator<T>, value: T | (() => T), constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validator = validator;\n\t\tthis.defaultValue = value;\n\t}\n\n\tpublic override default(value: Exclude<T, undefined> | (() => Exclude<T, undefined>)): DefaultValidator<Exclude<T, undefined>> {\n\t\tconst clone = this.clone() as unknown as <PERSON>faultValidator<Exclude<T, undefined>>;\n\t\tclone.defaultValue = value;\n\t\treturn clone;\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidatorError> {\n\t\treturn typeof value === 'undefined' //\n\t\t\t? Result.ok(getValue(this.defaultValue))\n\t\t\t: this.validator['handle'](value); // eslint-disable-line @typescript-eslint/dot-notation\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.defaultValue, this.constraints]);\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class CombinedError extends BaseError {\n\tpublic readonly errors: readonly BaseError[];\n\n\tpublic constructor(errors: readonly BaseError[]) {\n\t\tsuper('Received one or more errors');\n\n\t\tthis.errors = errors;\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize('[CombinedError]', 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1, compact: true };\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\n\t\tconst header = `${options.stylize('CombinedError', 'special')} (${options.stylize(this.errors.length.toString(), 'number')})`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst errors = this.errors\n\t\t\t.map((error, i) => {\n\t\t\t\tconst index = options.stylize((i + 1).toString(), 'number');\n\t\t\t\tconst body = error[customInspectSymbolStackLess](depth - 1, newOptions).replace(/\\n/g, padding);\n\n\t\t\t\treturn `  ${index} ${body}`;\n\t\t\t})\n\t\t\t.join('\\n\\n');\n\t\treturn `${header}\\n  ${message}\\n\\n${errors}`;\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedError } from '../lib/errors/CombinedError';\nimport type { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator, LiteralValidator, NullishValidator } from './imports';\n\nexport class UnionValidator<T> extends BaseValidator<T> {\n\tprivate validators: readonly BaseValidator<T>[];\n\n\tpublic constructor(validators: readonly BaseValidator<T>[], constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validators = validators;\n\t}\n\n\tpublic override get optional(): UnionValidator<T | undefined> {\n\t\tif (this.validators.length === 0) return new UnionValidator<T | undefined>([new LiteralValidator(undefined)], this.constraints);\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\t// If already optional, return a clone:\n\t\t\tif (validator.expected === undefined) return this.clone();\n\n\t\t\t// If it's nullable, convert the nullable validator into a nullish validator to optimize `null | undefined`:\n\t\t\tif (validator.expected === null) {\n\t\t\t\treturn new UnionValidator<T | null | undefined>(\n\t\t\t\t\t[new NullishValidator(), ...this.validators.slice(1)],\n\t\t\t\t\tthis.constraints\n\t\t\t\t) as UnionValidator<T | undefined>;\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t// If it's already nullish (which validates optional), return a clone:\n\t\t\treturn this.clone();\n\t\t}\n\n\t\treturn new UnionValidator([new LiteralValidator(undefined), ...this.validators]);\n\t}\n\n\tpublic get required(): UnionValidator<Exclude<T, undefined>> {\n\t\ttype RequiredValidator = UnionValidator<Exclude<T, undefined>>;\n\n\t\tif (this.validators.length === 0) return this.clone() as unknown as RequiredValidator;\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\tif (validator.expected === undefined) return new UnionValidator(this.validators.slice(1), this.constraints) as RequiredValidator;\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\treturn new UnionValidator([new LiteralValidator(null), ...this.validators.slice(1)], this.constraints) as RequiredValidator;\n\t\t}\n\n\t\treturn this.clone() as unknown as RequiredValidator;\n\t}\n\n\tpublic override get nullable(): UnionValidator<T | null> {\n\t\tif (this.validators.length === 0) return new UnionValidator<T | null>([new LiteralValidator(null)], this.constraints);\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\t// If already nullable, return a clone:\n\t\t\tif (validator.expected === null) return this.clone();\n\n\t\t\t// If it's optional, convert the optional validator into a nullish validator to optimize `null | undefined`:\n\t\t\tif (validator.expected === undefined) {\n\t\t\t\treturn new UnionValidator<T | null | undefined>(\n\t\t\t\t\t[new NullishValidator(), ...this.validators.slice(1)],\n\t\t\t\t\tthis.constraints\n\t\t\t\t) as UnionValidator<T | null>;\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t// If it's already nullish (which validates nullable), return a clone:\n\t\t\treturn this.clone();\n\t\t}\n\n\t\treturn new UnionValidator([new LiteralValidator(null), ...this.validators]);\n\t}\n\n\tpublic override get nullish(): UnionValidator<T | null | undefined> {\n\t\tif (this.validators.length === 0) return new UnionValidator<T | null | undefined>([new NullishValidator()], this.constraints);\n\n\t\tconst [validator] = this.validators;\n\t\tif (validator instanceof LiteralValidator) {\n\t\t\t// If already nullable or optional, promote the union to nullish:\n\t\t\tif (validator.expected === null || validator.expected === undefined) {\n\t\t\t\treturn new UnionValidator<T | null | undefined>([new NullishValidator(), ...this.validators.slice(1)], this.constraints);\n\t\t\t}\n\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t// If it's already nullish, return a clone:\n\t\t\treturn this.clone();\n\t\t}\n\n\t\treturn new UnionValidator<T | null | undefined>([new NullishValidator(), ...this.validators]);\n\t}\n\n\tpublic override or<O>(...predicates: readonly BaseValidator<O>[]): UnionValidator<T | O> {\n\t\treturn new UnionValidator<T | O>([...this.validators, ...predicates]);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validators, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError | CombinedError> {\n\t\tconst errors: BaseError[] = [];\n\n\t\tfor (const validator of this.validators) {\n\t\t\tconst result = validator.run(value);\n\t\t\tif (result.isOk()) return result as Result<T, CombinedError>;\n\t\t\terrors.push(result.error!);\n\t\t}\n\n\t\treturn Result.err(new CombinedError(errors));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { MissingPropertyError } from '../lib/errors/MissingPropertyError';\nimport { UnknownPropertyError } from '../lib/errors/UnknownPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport type { MappedObjectValidator, UndefinedToOptional } from '../lib/util-types';\nimport { BaseValidator } from './BaseValidator';\nimport { DefaultValidator } from './DefaultValidator';\nimport { LiteralValidator } from './LiteralValidator';\nimport { NullishValidator } from './NullishValidator';\nimport { UnionValidator } from './UnionValidator';\n\nexport class ObjectValidator<T extends object, I = UndefinedToOptional<T>> extends BaseValidator<I> {\n\tpublic readonly shape: MappedObjectValidator<T>;\n\tpublic readonly strategy: ObjectValidatorStrategy;\n\tprivate readonly keys: readonly (keyof I)[] = [];\n\tprivate readonly handleStrategy: (value: object) => Result<I, CombinedPropertyError>;\n\n\tprivate readonly requiredKeys = new Map<keyof I, BaseValidator<unknown>>();\n\tprivate readonly possiblyUndefinedKeys = new Map<keyof I, BaseValidator<unknown>>();\n\tprivate readonly possiblyUndefinedKeysWithDefaults = new Map<keyof I, DefaultValidator<unknown>>();\n\n\tpublic constructor(\n\t\tshape: MappedObjectValidator<T>,\n\t\tstrategy: ObjectValidatorStrategy = ObjectValidatorStrategy.Ignore,\n\t\tconstraints: readonly IConstraint<I>[] = []\n\t) {\n\t\tsuper(constraints);\n\t\tthis.shape = shape;\n\t\tthis.strategy = strategy;\n\n\t\tswitch (this.strategy) {\n\t\t\tcase ObjectValidatorStrategy.Ignore:\n\t\t\t\tthis.handleStrategy = (value) => this.handleIgnoreStrategy(value);\n\t\t\t\tbreak;\n\t\t\tcase ObjectValidatorStrategy.Strict: {\n\t\t\t\tthis.handleStrategy = (value) => this.handleStrictStrategy(value);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tcase ObjectValidatorStrategy.Passthrough:\n\t\t\t\tthis.handleStrategy = (value) => this.handlePassthroughStrategy(value);\n\t\t\t\tbreak;\n\t\t}\n\n\t\tconst shapeEntries = Object.entries(shape) as [keyof I, BaseValidator<T>][];\n\t\tthis.keys = shapeEntries.map(([key]) => key);\n\n\t\tfor (const [key, validator] of shapeEntries) {\n\t\t\tif (validator instanceof UnionValidator) {\n\t\t\t\t// eslint-disable-next-line @typescript-eslint/dot-notation\n\t\t\t\tconst [possiblyLiteralOrNullishPredicate] = validator['validators'];\n\n\t\t\t\tif (possiblyLiteralOrNullishPredicate instanceof NullishValidator) {\n\t\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t\t} else if (possiblyLiteralOrNullishPredicate instanceof LiteralValidator) {\n\t\t\t\t\tif (possiblyLiteralOrNullishPredicate.expected === undefined) {\n\t\t\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t\t\t}\n\t\t\t\t} else if (validator instanceof DefaultValidator) {\n\t\t\t\t\tthis.possiblyUndefinedKeysWithDefaults.set(key, validator);\n\t\t\t\t} else {\n\t\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t\t}\n\t\t\t} else if (validator instanceof NullishValidator) {\n\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t} else if (validator instanceof LiteralValidator) {\n\t\t\t\tif (validator.expected === undefined) {\n\t\t\t\t\tthis.possiblyUndefinedKeys.set(key, validator);\n\t\t\t\t} else {\n\t\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t\t}\n\t\t\t} else if (validator instanceof DefaultValidator) {\n\t\t\t\tthis.possiblyUndefinedKeysWithDefaults.set(key, validator);\n\t\t\t} else {\n\t\t\t\tthis.requiredKeys.set(key, validator);\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic get strict(): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, ObjectValidatorStrategy.Strict, this.constraints]);\n\t}\n\n\tpublic get ignore(): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, ObjectValidatorStrategy.Ignore, this.constraints]);\n\t}\n\n\tpublic get passthrough(): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, ObjectValidatorStrategy.Passthrough, this.constraints]);\n\t}\n\n\tpublic get partial(): ObjectValidator<{ [Key in keyof I]?: I[Key] }> {\n\t\tconst shape = Object.fromEntries(this.keys.map((key) => [key, this.shape[key as unknown as keyof typeof this.shape].optional]));\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, this.constraints]);\n\t}\n\n\tpublic get required(): ObjectValidator<{ [Key in keyof I]-?: I[Key] }> {\n\t\tconst shape = Object.fromEntries(\n\t\t\tthis.keys.map((key) => {\n\t\t\t\tlet validator = this.shape[key as unknown as keyof typeof this.shape];\n\t\t\t\tif (validator instanceof UnionValidator) validator = validator.required;\n\t\t\t\treturn [key, validator];\n\t\t\t})\n\t\t);\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, this.constraints]);\n\t}\n\n\tpublic extend<ET extends object>(schema: ObjectValidator<ET> | MappedObjectValidator<ET>): ObjectValidator<T & ET> {\n\t\tconst shape = { ...this.shape, ...(schema instanceof ObjectValidator ? schema.shape : schema) };\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, this.constraints]);\n\t}\n\n\tpublic pick<K extends keyof I>(keys: readonly K[]): ObjectValidator<{ [Key in keyof Pick<I, K>]: I[Key] }> {\n\t\tconst shape = Object.fromEntries(\n\t\t\tkeys.filter((key) => this.keys.includes(key)).map((key) => [key, this.shape[key as unknown as keyof typeof this.shape]])\n\t\t);\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, this.constraints]);\n\t}\n\n\tpublic omit<K extends keyof I>(keys: readonly K[]): ObjectValidator<{ [Key in keyof Omit<I, K>]: I[Key] }> {\n\t\tconst shape = Object.fromEntries(\n\t\t\tthis.keys.filter((key) => !keys.includes(key as any)).map((key) => [key, this.shape[key as unknown as keyof typeof this.shape]])\n\t\t);\n\t\treturn Reflect.construct(this.constructor, [shape, this.strategy, this.constraints]);\n\t}\n\n\tprotected override handle(value: unknown): Result<I, ValidationError | CombinedPropertyError> {\n\t\tconst typeOfValue = typeof value;\n\t\tif (typeOfValue !== 'object') {\n\t\t\treturn Result.err(new ValidationError('s.object(T)', `Expected the value to be an object, but received ${typeOfValue} instead`, value));\n\t\t}\n\n\t\tif (value === null) {\n\t\t\treturn Result.err(new ValidationError('s.object(T)', 'Expected the value to not be null', value));\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\treturn Result.err(new ValidationError('s.object(T)', 'Expected the value to not be an array', value));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(value as I);\n\t\t}\n\n\t\tfor (const predicate of Object.values(this.shape) as BaseValidator<any>[]) {\n\t\t\tpredicate.setParent(this.parent ?? value!);\n\t\t}\n\n\t\treturn this.handleStrategy(value as object);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.shape, this.strategy, this.constraints]);\n\t}\n\n\tprivate handleIgnoreStrategy(value: object): Result<I, CombinedPropertyError> {\n\t\tconst errors: [PropertyKey, BaseError][] = [];\n\t\tconst finalObject = {} as I;\n\t\tconst inputEntries = new Map(Object.entries(value) as [keyof I, unknown][]);\n\n\t\tconst runPredicate = (key: keyof I, predicate: BaseValidator<unknown>) => {\n\t\t\tconst result = predicate.run(value[key as keyof object]);\n\n\t\t\tif (result.isOk()) {\n\t\t\t\tfinalObject[key] = result.value as I[keyof I];\n\t\t\t} else {\n\t\t\t\tconst error = result.error!;\n\t\t\t\terrors.push([key, error]);\n\t\t\t}\n\t\t};\n\n\t\tfor (const [key, predicate] of this.requiredKeys) {\n\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\trunPredicate(key, predicate);\n\t\t\t} else {\n\t\t\t\terrors.push([key, new MissingPropertyError(key)]);\n\t\t\t}\n\t\t}\n\n\t\t// Run possibly undefined keys that also have defaults even if there are no more keys to process (this is necessary so we fill in those defaults)\n\t\tfor (const [key, validator] of this.possiblyUndefinedKeysWithDefaults) {\n\t\t\tinputEntries.delete(key);\n\t\t\trunPredicate(key, validator);\n\t\t}\n\n\t\t// Early exit if there are no more properties to validate in the object and there are errors to report\n\t\tif (inputEntries.size === 0) {\n\t\t\treturn errors.length === 0 //\n\t\t\t\t? Result.ok(finalObject)\n\t\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t\t}\n\n\t\t// In the event the remaining keys to check are less than the number of possible undefined keys, we check those\n\t\t// as it could yield a faster execution\n\t\tconst checkInputEntriesInsteadOfSchemaKeys = this.possiblyUndefinedKeys.size > inputEntries.size;\n\n\t\tif (checkInputEntriesInsteadOfSchemaKeys) {\n\t\t\tfor (const [key] of inputEntries) {\n\t\t\t\tconst predicate = this.possiblyUndefinedKeys.get(key);\n\n\t\t\t\tif (predicate) {\n\t\t\t\t\trunPredicate(key, predicate);\n\t\t\t\t}\n\t\t\t}\n\t\t} else {\n\t\t\tfor (const [key, predicate] of this.possiblyUndefinedKeys) {\n\t\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\t\trunPredicate(key, predicate);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(finalObject)\n\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t}\n\n\tprivate handleStrictStrategy(value: object): Result<I, CombinedPropertyError> {\n\t\tconst errors: [PropertyKey, BaseError][] = [];\n\t\tconst finalResult = {} as I;\n\t\tconst inputEntries = new Map(Object.entries(value) as [keyof I, unknown][]);\n\n\t\tconst runPredicate = (key: keyof I, predicate: BaseValidator<unknown>) => {\n\t\t\tconst result = predicate.run(value[key as keyof object]);\n\n\t\t\tif (result.isOk()) {\n\t\t\t\tfinalResult[key] = result.value as I[keyof I];\n\t\t\t} else {\n\t\t\t\tconst error = result.error!;\n\t\t\t\terrors.push([key, error]);\n\t\t\t}\n\t\t};\n\n\t\tfor (const [key, predicate] of this.requiredKeys) {\n\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\trunPredicate(key, predicate);\n\t\t\t} else {\n\t\t\t\terrors.push([key, new MissingPropertyError(key)]);\n\t\t\t}\n\t\t}\n\n\t\t// Run possibly undefined keys that also have defaults even if there are no more keys to process (this is necessary so we fill in those defaults)\n\t\tfor (const [key, validator] of this.possiblyUndefinedKeysWithDefaults) {\n\t\t\tinputEntries.delete(key);\n\t\t\trunPredicate(key, validator);\n\t\t}\n\n\t\tfor (const [key, predicate] of this.possiblyUndefinedKeys) {\n\t\t\t// All of these validators are assumed to be possibly undefined, so if we have gone through the entire object and there's still validators,\n\t\t\t// safe to assume we're done here\n\t\t\tif (inputEntries.size === 0) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (inputEntries.delete(key)) {\n\t\t\t\trunPredicate(key, predicate);\n\t\t\t}\n\t\t}\n\n\t\tif (inputEntries.size !== 0) {\n\t\t\tfor (const [key, value] of inputEntries.entries()) {\n\t\t\t\terrors.push([key, new UnknownPropertyError(key, value)]);\n\t\t\t}\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(finalResult)\n\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t}\n\n\tprivate handlePassthroughStrategy(value: object): Result<I, CombinedPropertyError> {\n\t\tconst result = this.handleIgnoreStrategy(value);\n\t\treturn result.isErr() ? result : Result.ok({ ...value, ...result.value } as I);\n\t}\n}\n\nexport enum ObjectValidatorStrategy {\n\tIgnore,\n\tStrict,\n\tPassthrough\n}\n", "import type { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class PassthroughValidator<T extends any | unknown> extends BaseValidator<T> {\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn Result.ok(value as T);\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class RecordValidator<T> extends BaseValidator<Record<string, T>> {\n\tprivate readonly validator: BaseValidator<T>;\n\n\tpublic constructor(validator: BaseValidator<T>, constraints: readonly IConstraint<Record<string, T>>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<Record<string, T>, ValidationError | CombinedPropertyError> {\n\t\tif (typeof value !== 'object') {\n\t\t\treturn Result.err(new ValidationError('s.record(T)', 'Expected an object', value));\n\t\t}\n\n\t\tif (value === null) {\n\t\t\treturn Result.err(new ValidationError('s.record(T)', 'Expected the value to not be null', value));\n\t\t}\n\n\t\tif (Array.isArray(value)) {\n\t\t\treturn Result.err(new ValidationError('s.record(T)', 'Expected the value to not be an array', value));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(value as Record<string, T>);\n\t\t}\n\n\t\tconst errors: [string, BaseError][] = [];\n\t\tconst transformed: Record<string, T> = {};\n\n\t\tfor (const [key, val] of Object.entries(value!)) {\n\t\t\tconst result = this.validator.run(val);\n\t\t\tif (result.isOk()) transformed[key] = result.value;\n\t\t\telse errors.push([key, result.error!]);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedError } from '../lib/errors/CombinedError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class SetValidator<T> extends BaseValidator<Set<T>> {\n\tprivate readonly validator: BaseValidator<T>;\n\n\tpublic constructor(validator: BaseValidator<T>, constraints: readonly IConstraint<Set<T>>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<Set<T>, ValidationError | CombinedError> {\n\t\tif (!(values instanceof Set)) {\n\t\t\treturn Result.err(new ValidationError('s.set(T)', 'Expected a set', values));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(values);\n\t\t}\n\n\t\tconst errors: BaseError[] = [];\n\t\tconst transformed = new Set<T>();\n\n\t\tfor (const value of values) {\n\t\t\tconst result = this.validator.run(value);\n\t\t\tif (result.isOk()) transformed.add(result.value);\n\t\t\telse errors.push(result.error!);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedError(errors));\n\t}\n}\n", "/**\n * @license MIT\n * @copyright 2020 <PERSON>\n * @see https://github.com/colinhacks/zod/blob/master/LICENSE\n */\nconst accountRegex = /^(?!\\.)(?!.*\\.\\.)([A-Z0-9_+-\\.]*)[A-Z0-9_+-]$/i;\n\n/**\n * Validates an email address string based on various checks:\n * - It must be a non nullish and non empty string\n * - It must include at least an `@` symbol`\n * - The account name may not exceed 64 characters\n * - The domain name may not exceed 255 characters\n * - The domain must include at least one `.` symbol\n * - Each part of the domain, split by `.` must not exceed 63 characters\n * - The email address must be [RFC-5322](https://datatracker.ietf.org/doc/html/rfc5322) compliant\n * @param email The email to validate\n * @returns `true` if the email is valid, `false` otherwise\n *\n * @remark Based on the following sources:\n * - `email-validator` by [manish<PERSON><PERSON>](https://github.com/manishsaraan) ([code](https://github.com/manishsaraan/email-validator/blob/master/index.js))\n * - [Comparing E-mail Address Validating Regular Expressions](http://fightingforalostcause.net/misc/2006/compare-email-regex.php)\n * - [Validating Email Addresses by Derrick Pallas](http://thedailywtf.com/Articles/Validating_Email_Addresses.aspx)\n * - [StackOverflow answer by bortzmeyer](http://stackoverflow.com/questions/201323/what-is-the-best-regular-expression-for-validating-email-addresses/201378#201378)\n * - [The wikipedia page on Email addresses](https://en.wikipedia.org/wiki/Email_address)\n */\nexport function validateEmail(email: string): boolean {\n\t// 1. Non-nullish and non-empty string check.\n\t//\n\t// If a nullish or empty email was provided then do an early exit\n\tif (!email) return false;\n\n\t// Find the location of the @ symbol:\n\tconst atIndex = email.indexOf('@');\n\n\t// 2. @ presence check.\n\t//\n\t// If the email does not have the @ symbol, it's automatically invalid:\n\tif (atIndex === -1) return false;\n\n\t// 3. <account> maximum length check.\n\t//\n\t// From <account>@<domain>, if <account> exceeds 64 characters, then the\n\t// position of the @ symbol is 64 or greater. In this case, the email is\n\t// invalid:\n\tif (atIndex > 64) return false;\n\n\tconst domainIndex = atIndex + 1;\n\n\t// 7.1. Duplicated @ symbol check.\n\t//\n\t// If there's a second @ symbol, the email is automatically invalid:\n\tif (email.includes('@', domainIndex)) return false;\n\n\t// 4. <domain> maximum length check.\n\t//\n\t// From <account>@<domain>, if <domain> exceeds 255 characters, then it\n\t// means that the amount of characters between the start of <domain> and the\n\t// end of the string is separated by 255 or more characters.\n\tif (email.length - domainIndex > 255) return false;\n\n\t// Find the location of the . symbol in <domain>:\n\tlet dotIndex = email.indexOf('.', domainIndex);\n\n\t// 5. <domain> dot (.) symbol check.\n\t//\n\t// From <account>@<domain>, if <domain> does not contain a dot (.) symbol,\n\t// then it means the domain is invalid.\n\tif (dotIndex === -1) return false;\n\n\t// 6. <domain> parts length.\n\t//\n\t// Assign a temporary variable to store the start of the last read domain\n\t// part, this would be at the start of <domain>.\n\t//\n\t// For a <domain> part to be correct, it must have at most, 63 characters.\n\t// We repeat this step for every sub-section of <domain> contained within\n\t// dot (.) symbols.\n\t//\n\t// The following step is a more optimized version of the following code:\n\t//\n\t// ```javascript\n\t// domain.split('.').some((part) => part.length > 63);\n\t// ```\n\tlet lastDotIndex = domainIndex;\n\tdo {\n\t\tif (dotIndex - lastDotIndex > 63) return false;\n\n\t\tlastDotIndex = dotIndex + 1;\n\t} while ((dotIndex = email.indexOf('.', lastDotIndex)) !== -1);\n\n\t// The loop iterates from the first to the n - 1 part, this line checks for\n\t// the last (n) part:\n\tif (email.length - lastDotIndex > 63) return false;\n\n\t// 7.2. Character checks.\n\t//\n\t// From <account>@<domain>:\n\t// - Extract the <account> part by slicing the input from start to the @\n\t//   character. Validate afterwards.\n\t// - Extract the <domain> part by slicing the input from the start of\n\t//   <domain>. Validate afterwards.\n\t//\n\t// Note: we inline the variables so <domain> isn't created unless the\n\t//       <account> check passes.\n\treturn accountRegex.test(email.slice(0, atIndex)) && validateEmailDomain(email.slice(domainIndex));\n}\n\nfunction validateEmailDomain(domain: string): boolean {\n\ttry {\n\t\treturn new URL(`http://${domain}`).hostname === domain;\n\t} catch {\n\t\treturn false;\n\t}\n}\n", "/**\n * Code ported from https://github.com/nodejs/node/blob/5fad0b93667ffc6e4def52996b9529ac99b26319/lib/internal/net.js\n */\n\n// IPv4 Segment\nconst v4Seg = '(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])';\nconst v4Str = `(${v4Seg}[.]){3}${v4Seg}`;\nconst IPv4Reg = new RegExp(`^${v4Str}$`);\n\n// IPv6 Segment\nconst v6Seg = '(?:[0-9a-fA-F]{1,4})';\nconst IPv6Reg = new RegExp(\n\t'^(' +\n\t\t`(?:${v6Seg}:){7}(?:${v6Seg}|:)|` +\n\t\t`(?:${v6Seg}:){6}(?:${v4Str}|:${v6Seg}|:)|` +\n\t\t`(?:${v6Seg}:){5}(?::${v4Str}|(:${v6Seg}){1,2}|:)|` +\n\t\t`(?:${v6Seg}:){4}(?:(:${v6Seg}){0,1}:${v4Str}|(:${v6Seg}){1,3}|:)|` +\n\t\t`(?:${v6Seg}:){3}(?:(:${v6Seg}){0,2}:${v4Str}|(:${v6Seg}){1,4}|:)|` +\n\t\t`(?:${v6Seg}:){2}(?:(:${v6Seg}){0,3}:${v4Str}|(:${v6Seg}){1,5}|:)|` +\n\t\t`(?:${v6Seg}:){1}(?:(:${v6Seg}){0,4}:${v4Str}|(:${v6Seg}){1,6}|:)|` +\n\t\t`(?::((?::${v6Seg}){0,5}:${v4Str}|(?::${v6Seg}){1,7}|:))` +\n\t\t')(%[0-9a-zA-Z-.:]{1,})?$'\n);\n\nexport function isIPv4(s: string): boolean {\n\treturn IPv4Reg.test(s);\n}\n\nexport function isIPv6(s: string): boolean {\n\treturn IPv6Reg.test(s);\n}\n\nexport function isIP(s: string): number {\n\tif (isIPv4(s)) return 4;\n\tif (isIPv6(s)) return 6;\n\treturn 0;\n}\n", "export const phoneNumberRegex = /^((?:\\+|0{0,2})\\d{1,2}\\s?)?\\(?\\d{3}\\)?[\\s.-]?\\d{3}[\\s.-]?\\d{4}$/;\n\nexport function validatePhoneNumber(input: string) {\n\treturn phoneNumberRegex.test(input);\n}\n", "import { inspect, type InspectOptionsStylized } from 'util';\nimport { customInspectSymbolStackLess } from './BaseError';\nimport { BaseConstraintError, type ConstraintErrorNames } from './BaseConstraintError';\n\nexport class MultiplePossibilitiesConstraintError<T = unknown> extends BaseConstraintError<T> {\n\tpublic readonly expected: readonly string[];\n\n\tpublic constructor(constraint: ConstraintErrorNames, message: string, given: T, expected: readonly string[]) {\n\t\tsuper(constraint, message, given);\n\t\tthis.expected = expected;\n\t}\n\n\tpublic toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tconstraint: this.constraint,\n\t\t\tgiven: this.given,\n\t\t\texpected: this.expected\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst constraint = options.stylize(this.constraint, 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[MultiplePossibilitiesConstraintError: ${constraint}]`, 'special');\n\t\t}\n\n\t\tconst newOptions = { ...options, depth: options.depth === null ? null : options.depth! - 1 };\n\n\t\tconst verticalLine = options.stylize('|', 'undefined');\n\t\tconst padding = `\\n  ${verticalLine} `;\n\t\tconst given = inspect(this.given, newOptions).replace(/\\n/g, padding);\n\n\t\tconst header = `${options.stylize('MultiplePossibilitiesConstraintError', 'special')} > ${constraint}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\n\t\tconst expectedPadding = `\\n  ${verticalLine} - `;\n\t\tconst expectedBlock = `\\n  ${options.stylize('Expected any of the following:', 'string')}${expectedPadding}${this.expected\n\t\t\t.map((possible) => options.stylize(possible, 'boolean'))\n\t\t\t.join(expectedPadding)}`;\n\t\tconst givenBlock = `\\n  ${options.stylize('Received:', 'regexp')}${padding}${given}`;\n\t\treturn `${header}\\n  ${message}\\n${expectedBlock}\\n${givenBlock}`;\n\t}\n}\n", "export function combinedErrorFn<P extends [...any], E extends Error>(...fns: ErrorFn<P, E>[]): ErrorFn<P, E> {\n\tswitch (fns.length) {\n\t\tcase 0:\n\t\t\treturn () => null;\n\t\tcase 1:\n\t\t\treturn fns[0];\n\t\tcase 2: {\n\t\t\tconst [fn0, fn1] = fns;\n\t\t\treturn (...params) => fn0(...params) || fn1(...params);\n\t\t}\n\t\tdefault: {\n\t\t\treturn (...params) => {\n\t\t\t\tfor (const fn of fns) {\n\t\t\t\t\tconst result = fn(...params);\n\t\t\t\t\tif (result) return result;\n\t\t\t\t}\n\n\t\t\t\treturn null;\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport type ErrorFn<P extends [...any], E extends Error> = (...params: P) => E | null;\n", "import { MultiplePossibilitiesConstraintError } from '../../lib/errors/MultiplePossibilitiesConstraintError';\nimport { combinedErrorFn, type ErrorFn } from './common/combinedResultFn';\n\nexport type StringProtocol = `${string}:`;\n\nexport type StringDomain = `${string}.${string}`;\n\nexport interface UrlOptions {\n\tallowedProtocols?: StringProtocol[];\n\tallowedDomains?: StringDomain[];\n}\n\nexport function createUrlValidators(options?: UrlOptions) {\n\tconst fns: ErrorFn<[input: string, url: URL], MultiplePossibilitiesConstraintError<string>>[] = [];\n\n\tif (options?.allowedProtocols?.length) fns.push(allowedProtocolsFn(options.allowedProtocols));\n\tif (options?.allowedDomains?.length) fns.push(allowedDomainsFn(options.allowedDomains));\n\n\treturn combinedErrorFn(...fns);\n}\n\nfunction allowedProtocolsFn(allowedProtocols: StringProtocol[]) {\n\treturn (input: string, url: URL) =>\n\t\tallowedProtocols.includes(url.protocol as StringProtocol)\n\t\t\t? null\n\t\t\t: new MultiplePossibilitiesConstraintError('s.string.url', 'Invalid URL protocol', input, allowedProtocols);\n}\n\nfunction allowedDomainsFn(allowedDomains: StringDomain[]) {\n\treturn (input: string, url: URL) =>\n\t\tallowedDomains.includes(url.hostname as StringDomain)\n\t\t\t? null\n\t\t\t: new MultiplePossibilitiesConstraintError('s.string.url', 'Invalid URL domain', input, allowedDomains);\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\nimport { validateEmail } from './util/emailValidator';\nimport { isIP, isIPv4, isIPv6 } from './util/net';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\nimport { validatePhoneNumber } from './util/phoneValidator';\nimport { createUrlValidators } from './util/urlValidators';\n\nexport type StringConstraintName = `s.string.${\n\t| `length${'LessThan' | 'LessThanOrEqual' | 'GreaterThan' | 'GreaterThanOrEqual' | 'Equal' | 'NotEqual'}`\n\t| 'regex'\n\t| 'url'\n\t| 'uuid'\n\t| 'email'\n\t| `ip${'v4' | 'v6' | ''}`\n\t| 'date'\n\t| 'phone'}`;\n\nexport type StringProtocol = `${string}:`;\n\nexport type StringDomain = `${string}.${string}`;\n\nexport interface UrlOptions {\n\tallowedProtocols?: StringProtocol[];\n\tallowedDomains?: StringDomain[];\n}\n\nexport type UUIDVersion = 1 | 3 | 4 | 5;\n\nexport interface StringUuidOptions {\n\tversion?: UUIDVersion | `${UUIDVersion}-${UUIDVersion}` | null;\n\tnullable?: boolean;\n}\n\nfunction stringLengthComparator(comparator: Comparator, name: StringConstraintName, expected: string, length: number): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn comparator(input.length, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid string length', input, expected));\n\t\t}\n\t};\n}\n\nexport function stringLengthLessThan(length: number): IConstraint<string> {\n\tconst expected = `expected.length < ${length}`;\n\treturn stringLengthComparator(lessThan, 's.string.lengthLessThan', expected, length);\n}\n\nexport function stringLengthLessThanOrEqual(length: number): IConstraint<string> {\n\tconst expected = `expected.length <= ${length}`;\n\treturn stringLengthComparator(lessThanOrEqual, 's.string.lengthLessThanOrEqual', expected, length);\n}\n\nexport function stringLengthGreaterThan(length: number): IConstraint<string> {\n\tconst expected = `expected.length > ${length}`;\n\treturn stringLengthComparator(greaterThan, 's.string.lengthGreaterThan', expected, length);\n}\n\nexport function stringLengthGreaterThanOrEqual(length: number): IConstraint<string> {\n\tconst expected = `expected.length >= ${length}`;\n\treturn stringLengthComparator(greaterThanOrEqual, 's.string.lengthGreaterThanOrEqual', expected, length);\n}\n\nexport function stringLengthEqual(length: number): IConstraint<string> {\n\tconst expected = `expected.length === ${length}`;\n\treturn stringLengthComparator(equal, 's.string.lengthEqual', expected, length);\n}\n\nexport function stringLengthNotEqual(length: number): IConstraint<string> {\n\tconst expected = `expected.length !== ${length}`;\n\treturn stringLengthComparator(notEqual, 's.string.lengthNotEqual', expected, length);\n}\n\nexport function stringEmail(): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn validateEmail(input)\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.string.email', 'Invalid email address', input, 'expected to be an email address'));\n\t\t}\n\t};\n}\n\nfunction stringRegexValidator(type: StringConstraintName, expected: string, regex: RegExp): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn regex.test(input) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(type, 'Invalid string format', input, expected));\n\t\t}\n\t};\n}\n\nexport function stringUrl(options?: UrlOptions): IConstraint<string> {\n\tconst validatorFn = createUrlValidators(options);\n\treturn {\n\t\trun(input: string) {\n\t\t\tlet url: URL;\n\t\t\ttry {\n\t\t\t\turl = new URL(input);\n\t\t\t} catch {\n\t\t\t\treturn Result.err(new ExpectedConstraintError('s.string.url', 'Invalid URL', input, 'expected to match a URL'));\n\t\t\t}\n\n\t\t\tconst validatorFnResult = validatorFn(input, url);\n\t\t\tif (validatorFnResult === null) return Result.ok(input);\n\t\t\treturn Result.err(validatorFnResult);\n\t\t}\n\t};\n}\n\nexport function stringIp(version?: 4 | 6): IConstraint<string> {\n\tconst ipVersion = version ? (`v${version}` as const) : '';\n\tconst validatorFn = version === 4 ? isIPv4 : version === 6 ? isIPv6 : isIP;\n\n\tconst name = `s.string.ip${ipVersion}` as const;\n\tconst message = `Invalid IP${ipVersion} address`;\n\tconst expected = `expected to be an IP${ipVersion} address`;\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn validatorFn(input) ? Result.ok(input) : Result.err(new ExpectedConstraintError(name, message, input, expected));\n\t\t}\n\t};\n}\n\nexport function stringRegex(regex: RegExp) {\n\treturn stringRegexValidator('s.string.regex', `expected ${regex}.test(expected) to be true`, regex);\n}\n\nexport function stringUuid({ version = 4, nullable = false }: StringUuidOptions = {}) {\n\tversion ??= '1-5';\n\tconst regex = new RegExp(\n\t\t`^(?:[0-9A-F]{8}-[0-9A-F]{4}-[${version}][0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}${\n\t\t\tnullable ? '|00000000-0000-0000-0000-000000000000' : ''\n\t\t})$`,\n\t\t'i'\n\t);\n\tconst expected = `expected to match UUID${typeof version === 'number' ? `v${version}` : ` in range of ${version}`}`;\n\treturn stringRegexValidator('s.string.uuid', expected, regex);\n}\n\nexport function stringDate(): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\tconst time = Date.parse(input);\n\n\t\t\treturn Number.isNaN(time)\n\t\t\t\t? Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError(\n\t\t\t\t\t\t\t's.string.date',\n\t\t\t\t\t\t\t'Invalid date string',\n\t\t\t\t\t\t\tinput,\n\t\t\t\t\t\t\t'expected to be a valid date string (in the ISO 8601 or ECMA-262 format)'\n\t\t\t\t\t\t)\n\t\t\t\t  )\n\t\t\t\t: Result.ok(input);\n\t\t}\n\t};\n}\n\nexport function stringPhone(): IConstraint<string> {\n\treturn {\n\t\trun(input: string) {\n\t\t\treturn validatePhoneNumber(input)\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.string.phone', 'Invalid phone number', input, 'expected to be a phone number'));\n\t\t}\n\t};\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\tstringDate,\n\tstringEmail,\n\tstringIp,\n\tstringLengthEqual,\n\tstringLengthGreaterThan,\n\tstringLengthGreaterThanOrEqual,\n\tstringLengthLessThan,\n\tstringLengthLessThanOrEqual,\n\tstringLengthNotEqual,\n\tstringPhone,\n\tstringRegex,\n\tstringUrl,\n\tstringUuid,\n\ttype StringUuidOptions,\n\ttype UrlOptions\n} from '../constraints/StringConstraints';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class StringValidator<T extends string> extends BaseValidator<T> {\n\tpublic lengthLessThan(length: number): this {\n\t\treturn this.addConstraint(stringLengthLessThan(length) as IConstraint<T>);\n\t}\n\n\tpublic lengthLessThanOrEqual(length: number): this {\n\t\treturn this.addConstraint(stringLengthLessThanOrEqual(length) as IConstraint<T>);\n\t}\n\n\tpublic lengthGreaterThan(length: number): this {\n\t\treturn this.addConstraint(stringLengthGreaterThan(length) as IConstraint<T>);\n\t}\n\n\tpublic lengthGreaterThanOrEqual(length: number): this {\n\t\treturn this.addConstraint(stringLengthGreaterThanOrEqual(length) as IConstraint<T>);\n\t}\n\n\tpublic lengthEqual(length: number): this {\n\t\treturn this.addConstraint(stringLengthEqual(length) as IConstraint<T>);\n\t}\n\n\tpublic lengthNotEqual(length: number): this {\n\t\treturn this.addConstraint(stringLengthNotEqual(length) as IConstraint<T>);\n\t}\n\n\tpublic get email(): this {\n\t\treturn this.addConstraint(stringEmail() as IConstraint<T>);\n\t}\n\n\tpublic url(options?: UrlOptions): this {\n\t\treturn this.addConstraint(stringUrl(options) as IConstraint<T>);\n\t}\n\n\tpublic uuid(options?: StringUuidOptions): this {\n\t\treturn this.addConstraint(stringUuid(options) as IConstraint<T>);\n\t}\n\n\tpublic regex(regex: RegExp): this {\n\t\treturn this.addConstraint(stringRegex(regex) as IConstraint<T>);\n\t}\n\n\tpublic get date() {\n\t\treturn this.addConstraint(stringDate() as IConstraint<T>);\n\t}\n\n\tpublic get ipv4(): this {\n\t\treturn this.ip(4);\n\t}\n\n\tpublic get ipv6(): this {\n\t\treturn this.ip(6);\n\t}\n\n\tpublic ip(version?: 4 | 6): this {\n\t\treturn this.addConstraint(stringIp(version) as IConstraint<T>);\n\t}\n\n\tpublic phone(): this {\n\t\treturn this.addConstraint(stringPhone() as IConstraint<T>);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn typeof value === 'string' //\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.string', 'Expected a string primitive', value));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class TupleValidator<T extends any[]> extends BaseValidator<[...T]> {\n\tprivate readonly validators: BaseValidator<[...T]>[] = [];\n\n\tpublic constructor(validators: BaseValidator<[...T]>[], constraints: readonly IConstraint<[...T]>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validators = validators;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validators, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<[...T], ValidationError | CombinedPropertyError> {\n\t\tif (!Array.isArray(values)) {\n\t\t\treturn Result.err(new ValidationError('s.tuple(T)', 'Expected an array', values));\n\t\t}\n\n\t\tif (values.length !== this.validators.length) {\n\t\t\treturn Result.err(new ValidationError('s.tuple(T)', `Expected an array of length ${this.validators.length}`, values));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(values as [...T]);\n\t\t}\n\n\t\tconst errors: [number, BaseError][] = [];\n\t\tconst transformed: T = [] as unknown as T;\n\n\t\tfor (let i = 0; i < values.length; i++) {\n\t\t\tconst result = this.validators[i].run(values[i]);\n\t\t\tif (result.isOk()) transformed.push(result.value);\n\t\t\telse errors.push([i, result.error!]);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t}\n}\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport type { BaseError } from '../lib/errors/BaseError';\nimport { CombinedPropertyError } from '../lib/errors/CombinedPropertyError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class MapValidator<K, V> extends BaseValidator<Map<K, V>> {\n\tprivate readonly keyValidator: BaseValidator<K>;\n\tprivate readonly valueValidator: BaseValidator<V>;\n\n\tpublic constructor(keyValidator: BaseValidator<K>, valueValidator: BaseValidator<V>, constraints: readonly IConstraint<Map<K, V>>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.keyValidator = keyValidator;\n\t\tthis.valueValidator = valueValidator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.keyValidator, this.valueValidator, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<Map<K, V>, ValidationError | CombinedPropertyError> {\n\t\tif (!(value instanceof Map)) {\n\t\t\treturn Result.err(new ValidationError('s.map(K, V)', 'Expected a map', value));\n\t\t}\n\n\t\tif (!this.shouldRunConstraints) {\n\t\t\treturn Result.ok(value);\n\t\t}\n\n\t\tconst errors: [string, BaseError][] = [];\n\t\tconst transformed = new Map<K, V>();\n\n\t\tfor (const [key, val] of value.entries()) {\n\t\t\tconst keyResult = this.keyValidator.run(key);\n\t\t\tconst valueResult = this.valueValidator.run(val);\n\t\t\tconst { length } = errors;\n\t\t\tif (keyResult.isErr()) errors.push([key, keyResult.error]);\n\t\t\tif (valueResult.isErr()) errors.push([key, valueResult.error]);\n\t\t\tif (errors.length === length) transformed.set(keyResult.value!, valueResult.value!);\n\t\t}\n\n\t\treturn errors.length === 0 //\n\t\t\t? Result.ok(transformed)\n\t\t\t: Result.err(new CombinedPropertyError(errors));\n\t}\n}\n", "import type { Result } from '../lib/Result';\nimport type { IConstraint, Unwrap } from '../type-exports';\nimport { BaseValidator, type ValidatorError } from './imports';\n\nexport class LazyValidator<T extends BaseValidator<unknown>, R = Unwrap<T>> extends BaseValidator<R> {\n\tprivate readonly validator: (value: unknown) => T;\n\n\tpublic constructor(validator: (value: unknown) => T, constraints: readonly IConstraint<R>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.validator = validator;\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.validator, this.constraints]);\n\t}\n\n\tprotected handle(values: unknown): Result<R, ValidatorError> {\n\t\treturn this.validator(values).run(values) as Result<R, ValidatorError>;\n\t}\n}\n", "import type { InspectOptionsStylized } from 'util';\nimport { BaseError, customInspectSymbolStackLess } from './BaseError';\n\nexport class UnknownEnumValueError extends BaseError {\n\tpublic readonly value: string | number;\n\tpublic readonly enumKeys: string[];\n\tpublic readonly enumMappings: Map<string | number, string | number>;\n\n\tpublic constructor(value: string | number, keys: string[], enumMappings: Map<string | number, string | number>) {\n\t\tsuper('Expected the value to be one of the following enum values:');\n\n\t\tthis.value = value;\n\t\tthis.enumKeys = keys;\n\t\tthis.enumMappings = enumMappings;\n\t}\n\n\tpublic toJSON() {\n\t\treturn {\n\t\t\tname: this.name,\n\t\t\tvalue: this.value,\n\t\t\tenumKeys: this.enumKeys,\n\t\t\tenumMappings: [...this.enumMappings.entries()]\n\t\t};\n\t}\n\n\tprotected [customInspectSymbolStackLess](depth: number, options: InspectOptionsStylized): string {\n\t\tconst value = options.stylize(this.value.toString(), 'string');\n\t\tif (depth < 0) {\n\t\t\treturn options.stylize(`[UnknownEnumValueError: ${value}]`, 'special');\n\t\t}\n\n\t\tconst padding = `\\n  ${options.stylize('|', 'undefined')} `;\n\t\tconst pairs = this.enumKeys\n\t\t\t.map((key) => {\n\t\t\t\tconst enumValue = this.enumMappings.get(key)!;\n\t\t\t\treturn `${options.stylize(key, 'string')} or ${options.stylize(\n\t\t\t\t\tenumValue.toString(),\n\t\t\t\t\ttypeof enumValue === 'number' ? 'number' : 'string'\n\t\t\t\t)}`;\n\t\t\t})\n\t\t\t.join(padding);\n\n\t\tconst header = `${options.stylize('UnknownEnumValueError', 'special')} > ${value}`;\n\t\tconst message = options.stylize(this.message, 'regexp');\n\t\tconst pairsBlock = `${padding}${pairs}`;\n\t\treturn `${header}\\n  ${message}\\n${pairsBlock}`;\n\t}\n}\n", "import { UnknownEnumValueError } from '../lib/errors/UnknownEnumValueError';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class NativeEnumValidator<T extends NativeEnumLike> extends BaseValidator<T[keyof T]> {\n\tpublic readonly enumShape: T;\n\tpublic readonly hasNumericElements: boolean = false;\n\tprivate readonly enumKeys: string[];\n\tprivate readonly enumMapping = new Map<string | number, T[keyof T]>();\n\n\tpublic constructor(enumShape: T) {\n\t\tsuper();\n\t\tthis.enumShape = enumShape;\n\n\t\tthis.enumKeys = Object.keys(enumShape).filter((key) => {\n\t\t\treturn typeof enumShape[enumShape[key]] !== 'number';\n\t\t});\n\n\t\tfor (const key of this.enumKeys) {\n\t\t\tconst enumValue = enumShape[key] as T[keyof T];\n\n\t\t\tthis.enumMapping.set(key, enumValue);\n\t\t\tthis.enumMapping.set(enumValue, enumValue);\n\n\t\t\tif (typeof enumValue === 'number') {\n\t\t\t\tthis.hasNumericElements = true;\n\t\t\t\tthis.enumMapping.set(`${enumValue}`, enumValue);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected override handle(value: unknown): Result<T[keyof T], ValidationError | UnknownEnumValueError> {\n\t\tconst typeOfValue = typeof value;\n\n\t\tif (typeOfValue === 'number') {\n\t\t\tif (!this.hasNumericElements) {\n\t\t\t\treturn Result.err(new ValidationError('s.nativeEnum(T)', 'Expected the value to be a string', value));\n\t\t\t}\n\t\t} else if (typeOfValue !== 'string') {\n\t\t\t// typeOfValue !== 'number' is implied here\n\t\t\treturn Result.err(new ValidationError('s.nativeEnum(T)', 'Expected the value to be a string or number', value));\n\t\t}\n\n\t\tconst casted = value as string | number;\n\n\t\tconst possibleEnumValue = this.enumMapping.get(casted);\n\n\t\treturn typeof possibleEnumValue === 'undefined'\n\t\t\t? Result.err(new UnknownEnumValueError(casted, this.enumKeys, this.enumMapping))\n\t\t\t: Result.ok(possibleEnumValue);\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.enumShape]);\n\t}\n}\n\nexport interface NativeEnumLike {\n\t[key: string]: string | number;\n\t[key: number]: string;\n}\n", "import { ExpectedConstraintError } from '../lib/errors/ExpectedConstraintError';\nimport { Result } from '../lib/Result';\nimport type { IConstraint } from './base/IConstraint';\nimport { equal, greaterThan, greaterThanOrEqual, lessThan, lessThanOrEqual, notEqual, type Comparator } from './util/operators';\nimport type { TypedArray } from './util/typedArray';\n\nexport type TypedArrayConstraintName = `s.typedArray(T).${'byteLength' | 'length'}${\n\t| 'LessThan'\n\t| 'LessThanOrEqual'\n\t| 'GreaterThan'\n\t| 'GreaterThanOrEqual'\n\t| 'Equal'\n\t| 'NotEqual'\n\t| 'Range'\n\t| 'RangeInclusive'\n\t| 'RangeExclusive'}`;\n\nfunction typedArrayByteLengthComparator<T extends TypedArray>(\n\tcomparator: Comparator,\n\tname: TypedArrayConstraintName,\n\texpected: string,\n\tlength: number\n): IConstraint<T> {\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn comparator(input.byteLength, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid Typed Array byte length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayByteLengthLessThan<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.byteLength < ${value}`;\n\treturn typedArrayByteLengthComparator(lessThan, 's.typedArray(T).byteLengthLessThan', expected, value);\n}\n\nexport function typedArrayByteLengthLessThanOrEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.byteLength <= ${value}`;\n\treturn typedArrayByteLengthComparator(lessThanOrEqual, 's.typedArray(T).byteLengthLessThanOrEqual', expected, value);\n}\n\nexport function typedArrayByteLengthGreaterThan<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.byteLength > ${value}`;\n\treturn typedArrayByteLengthComparator(greaterThan, 's.typedArray(T).byteLengthGreaterThan', expected, value);\n}\n\nexport function typedArrayByteLengthGreaterThanOrEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.byteLength >= ${value}`;\n\treturn typedArrayByteLengthComparator(greaterThanOrEqual, 's.typedArray(T).byteLengthGreaterThanOrEqual', expected, value);\n}\n\nexport function typedArrayByteLengthEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.byteLength === ${value}`;\n\treturn typedArrayByteLengthComparator(equal, 's.typedArray(T).byteLengthEqual', expected, value);\n}\n\nexport function typedArrayByteLengthNotEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.byteLength !== ${value}`;\n\treturn typedArrayByteLengthComparator(notEqual, 's.typedArray(T).byteLengthNotEqual', expected, value);\n}\n\nexport function typedArrayByteLengthRange<T extends TypedArray>(start: number, endBefore: number): IConstraint<T> {\n\tconst expected = `expected.byteLength >= ${start} && expected.byteLength < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.byteLength >= start && input.byteLength < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.typedArray(T).byteLengthRange', 'Invalid Typed Array byte length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayByteLengthRangeInclusive<T extends TypedArray>(start: number, end: number) {\n\tconst expected = `expected.byteLength >= ${start} && expected.byteLength <= ${end}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.byteLength >= start && input.byteLength <= end //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError('s.typedArray(T).byteLengthRangeInclusive', 'Invalid Typed Array byte length', input, expected)\n\t\t\t\t  );\n\t\t}\n\t};\n}\n\nexport function typedArrayByteLengthRangeExclusive<T extends TypedArray>(startAfter: number, endBefore: number): IConstraint<T> {\n\tconst expected = `expected.byteLength > ${startAfter} && expected.byteLength < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.byteLength > startAfter && input.byteLength < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(\n\t\t\t\t\t\tnew ExpectedConstraintError('s.typedArray(T).byteLengthRangeExclusive', 'Invalid Typed Array byte length', input, expected)\n\t\t\t\t  );\n\t\t}\n\t};\n}\n\nfunction typedArrayLengthComparator<T extends TypedArray>(\n\tcomparator: Comparator,\n\tname: TypedArrayConstraintName,\n\texpected: string,\n\tlength: number\n): IConstraint<T> {\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn comparator(input.length, length) //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError(name, 'Invalid Typed Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayLengthLessThan<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.length < ${value}`;\n\treturn typedArrayLengthComparator(lessThan, 's.typedArray(T).lengthLessThan', expected, value);\n}\n\nexport function typedArrayLengthLessThanOrEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.length <= ${value}`;\n\treturn typedArrayLengthComparator(lessThanOrEqual, 's.typedArray(T).lengthLessThanOrEqual', expected, value);\n}\n\nexport function typedArrayLengthGreaterThan<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.length > ${value}`;\n\treturn typedArrayLengthComparator(greaterThan, 's.typedArray(T).lengthGreaterThan', expected, value);\n}\n\nexport function typedArrayLengthGreaterThanOrEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.length >= ${value}`;\n\treturn typedArrayLengthComparator(greaterThanOrEqual, 's.typedArray(T).lengthGreaterThanOrEqual', expected, value);\n}\n\nexport function typedArrayLengthEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.length === ${value}`;\n\treturn typedArrayLengthComparator(equal, 's.typedArray(T).lengthEqual', expected, value);\n}\n\nexport function typedArrayLengthNotEqual<T extends TypedArray>(value: number): IConstraint<T> {\n\tconst expected = `expected.length !== ${value}`;\n\treturn typedArrayLengthComparator(notEqual, 's.typedArray(T).lengthNotEqual', expected, value);\n}\n\nexport function typedArrayLengthRange<T extends TypedArray>(start: number, endBefore: number): IConstraint<T> {\n\tconst expected = `expected.length >= ${start} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.length >= start && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.typedArray(T).lengthRange', 'Invalid Typed Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayLengthRangeInclusive<T extends TypedArray>(start: number, end: number): IConstraint<T> {\n\tconst expected = `expected.length >= ${start} && expected.length <= ${end}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.length >= start && input.length <= end //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.typedArray(T).lengthRangeInclusive', 'Invalid Typed Array length', input, expected));\n\t\t}\n\t};\n}\n\nexport function typedArrayLengthRangeExclusive<T extends TypedArray>(startAfter: number, endBefore: number): IConstraint<T> {\n\tconst expected = `expected.length > ${startAfter} && expected.length < ${endBefore}`;\n\treturn {\n\t\trun(input: T) {\n\t\t\treturn input.length > startAfter && input.length < endBefore //\n\t\t\t\t? Result.ok(input)\n\t\t\t\t: Result.err(new ExpectedConstraintError('s.typedArray(T).lengthRangeExclusive', 'Invalid Typed Array length', input, expected));\n\t\t}\n\t};\n}\n", "const vowels = ['a', 'e', 'i', 'o', 'u'];\n\nexport const aOrAn = (word: string) => {\n\treturn `${vowels.includes(word[0].toLowerCase()) ? 'an' : 'a'} ${word}`;\n};\n", "export type TypedArray =\n\t| Int8Array\n\t| Uint8Array\n\t| Uint8ClampedArray\n\t| Int16Array\n\t| Uint16Array\n\t| Int32Array\n\t| Uint32Array\n\t| Float32Array\n\t| Float64Array\n\t| BigInt64Array\n\t| BigUint64Array;\n\nexport const TypedArrays = {\n\tInt8Array: (x: unknown): x is Int8Array => x instanceof Int8Array,\n\tUint8Array: (x: unknown): x is Uint8Array => x instanceof Uint8Array,\n\tUint8ClampedArray: (x: unknown): x is Uint8ClampedArray => x instanceof Uint8ClampedArray,\n\tInt16Array: (x: unknown): x is Int16Array => x instanceof Int16Array,\n\tUint16Array: (x: unknown): x is Uint16Array => x instanceof Uint16Array,\n\tInt32Array: (x: unknown): x is Int32Array => x instanceof Int32Array,\n\tUint32Array: (x: unknown): x is Uint32Array => x instanceof Uint32Array,\n\tFloat32Array: (x: unknown): x is Float32Array => x instanceof Float32Array,\n\tFloat64Array: (x: unknown): x is Float64Array => x instanceof Float64Array,\n\tBigInt64Array: (x: unknown): x is BigInt64Array => x instanceof BigInt64Array,\n\tBigUint64Array: (x: unknown): x is BigUint64Array => x instanceof BigUint64Array,\n\tTypedArray: (x: unknown): x is TypedArray => ArrayBuffer.isView(x) && !(x instanceof DataView)\n} as const;\n\nexport type TypedArrayName = keyof typeof TypedArrays;\n", "import type { IConstraint } from '../constraints/base/IConstraint';\nimport {\n\ttypedArrayByteLengthEqual,\n\ttypedArrayByteLengthGreaterThan,\n\ttypedArrayByteLengthGreaterThanOrEqual,\n\ttypedArrayByteLengthLessThan,\n\ttypedArrayByteLengthLessThanOrEqual,\n\ttypedArrayByteLengthNotEqual,\n\ttypedArrayByteLengthRange,\n\ttypedArrayByteLengthRangeExclusive,\n\ttypedArrayByteLengthRangeInclusive,\n\ttypedArrayLengthEqual,\n\ttypedArrayLengthGreaterThan,\n\ttypedArrayLengthGreaterThanOrEqual,\n\ttypedArrayLengthLessThan,\n\ttypedArrayLengthLessThanOrEqual,\n\ttypedArrayLengthNotEqual,\n\ttypedArrayLengthRange,\n\ttypedArrayLengthRangeExclusive,\n\ttypedArrayLengthRangeInclusive\n} from '../constraints/TypedArrayLengthConstraints';\nimport { aOrAn } from '../constraints/util/common/vowels';\nimport { TypedArrays, type TypedArray, type TypedArrayName } from '../constraints/util/typedArray';\nimport { ValidationError } from '../lib/errors/ValidationError';\nimport { Result } from '../lib/Result';\nimport { BaseValidator } from './imports';\n\nexport class TypedArrayValidator<T extends TypedArray> extends BaseValidator<T> {\n\tprivate readonly type: TypedArrayName;\n\n\tpublic constructor(type: TypedArrayName, constraints: readonly IConstraint<T>[] = []) {\n\t\tsuper(constraints);\n\t\tthis.type = type;\n\t}\n\n\tpublic byteLengthLessThan(length: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthLessThan(length));\n\t}\n\n\tpublic byteLengthLessThanOrEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthLessThanOrEqual(length));\n\t}\n\n\tpublic byteLengthGreaterThan(length: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthGreaterThan(length));\n\t}\n\n\tpublic byteLengthGreaterThanOrEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthGreaterThanOrEqual(length));\n\t}\n\n\tpublic byteLengthEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthEqual(length));\n\t}\n\n\tpublic byteLengthNotEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthNotEqual(length));\n\t}\n\n\tpublic byteLengthRange(start: number, endBefore: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthRange(start, endBefore));\n\t}\n\n\tpublic byteLengthRangeInclusive(startAt: number, endAt: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthRangeInclusive(startAt, endAt) as IConstraint<T>);\n\t}\n\n\tpublic byteLengthRangeExclusive(startAfter: number, endBefore: number) {\n\t\treturn this.addConstraint(typedArrayByteLengthRangeExclusive(startAfter, endBefore));\n\t}\n\n\tpublic lengthLessThan(length: number) {\n\t\treturn this.addConstraint(typedArrayLengthLessThan(length));\n\t}\n\n\tpublic lengthLessThanOrEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayLengthLessThanOrEqual(length));\n\t}\n\n\tpublic lengthGreaterThan(length: number) {\n\t\treturn this.addConstraint(typedArrayLengthGreaterThan(length));\n\t}\n\n\tpublic lengthGreaterThanOrEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayLengthGreaterThanOrEqual(length));\n\t}\n\n\tpublic lengthEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayLengthEqual(length));\n\t}\n\n\tpublic lengthNotEqual(length: number) {\n\t\treturn this.addConstraint(typedArrayLengthNotEqual(length));\n\t}\n\n\tpublic lengthRange(start: number, endBefore: number) {\n\t\treturn this.addConstraint(typedArrayLengthRange(start, endBefore));\n\t}\n\n\tpublic lengthRangeInclusive(startAt: number, endAt: number) {\n\t\treturn this.addConstraint(typedArrayLengthRangeInclusive(startAt, endAt));\n\t}\n\n\tpublic lengthRangeExclusive(startAfter: number, endBefore: number) {\n\t\treturn this.addConstraint(typedArrayLengthRangeExclusive(startAfter, endBefore));\n\t}\n\n\tprotected override clone(): this {\n\t\treturn Reflect.construct(this.constructor, [this.type, this.constraints]);\n\t}\n\n\tprotected handle(value: unknown): Result<T, ValidationError> {\n\t\treturn TypedArrays[this.type](value)\n\t\t\t? Result.ok(value as T)\n\t\t\t: Result.err(new ValidationError('s.typedArray', `Expected ${aOrAn(this.type)}`, value));\n\t}\n}\n", "import type { TypedArray, TypedArrayName } from '../constraints/util/typedArray';\nimport type { Unwrap, UnwrapTuple } from '../lib/util-types';\nimport {\n\tArrayValidator,\n\tBaseValidator,\n\tBigIntValidator,\n\tBooleanValidator,\n\tDateValidator,\n\tInstanceValidator,\n\tLiteralValidator,\n\tMapValidator,\n\tNeverValidator,\n\tNullishValidator,\n\tNumberValidator,\n\tObjectValidator,\n\tPassthroughValidator,\n\tRecordValidator,\n\tSetValidator,\n\tStringValidator,\n\tTupleValidator,\n\tUnionValidator\n} from '../validators/imports';\nimport { LazyValidator } from '../validators/LazyValidator';\nimport { NativeEnumValidator, type NativeEnumLike } from '../validators/NativeEnumValidator';\nimport { TypedArrayValidator } from '../validators/TypedArrayValidator';\nimport type { Constructor, MappedObjectValidator } from './util-types';\n\nexport class Shapes {\n\tpublic get string() {\n\t\treturn new StringValidator();\n\t}\n\n\tpublic get number() {\n\t\treturn new NumberValidator();\n\t}\n\n\tpublic get bigint() {\n\t\treturn new BigIntValidator();\n\t}\n\n\tpublic get boolean() {\n\t\treturn new BooleanValidator();\n\t}\n\n\tpublic get date() {\n\t\treturn new DateValidator();\n\t}\n\n\tpublic object<T extends object>(shape: MappedObjectValidator<T>) {\n\t\treturn new ObjectValidator<T>(shape);\n\t}\n\n\tpublic get undefined() {\n\t\treturn this.literal(undefined);\n\t}\n\n\tpublic get null() {\n\t\treturn this.literal(null);\n\t}\n\n\tpublic get nullish() {\n\t\treturn new NullishValidator();\n\t}\n\n\tpublic get any() {\n\t\treturn new PassthroughValidator<any>();\n\t}\n\n\tpublic get unknown() {\n\t\treturn new PassthroughValidator<unknown>();\n\t}\n\n\tpublic get never() {\n\t\treturn new NeverValidator();\n\t}\n\n\tpublic enum<T>(...values: readonly T[]) {\n\t\treturn this.union(...values.map((value) => this.literal(value)));\n\t}\n\n\tpublic nativeEnum<T extends NativeEnumLike>(enumShape: T): NativeEnumValidator<T> {\n\t\treturn new NativeEnumValidator(enumShape);\n\t}\n\n\tpublic literal<T>(value: T): BaseValidator<T> {\n\t\tif (value instanceof Date) return this.date.equal(value) as unknown as BaseValidator<T>;\n\t\treturn new LiteralValidator(value);\n\t}\n\n\tpublic instance<T>(expected: Constructor<T>): InstanceValidator<T> {\n\t\treturn new InstanceValidator(expected);\n\t}\n\n\tpublic union<T extends [...BaseValidator<any>[]]>(...validators: [...T]): UnionValidator<Unwrap<T[number]>> {\n\t\treturn new UnionValidator(validators);\n\t}\n\n\tpublic array<T>(validator: BaseValidator<T[][number]>): ArrayValidator<T[], T[][number]>;\n\tpublic array<T extends unknown[]>(validator: BaseValidator<T[number]>): ArrayValidator<T, T[number]>;\n\tpublic array<T extends unknown[]>(validator: BaseValidator<T[number]>) {\n\t\treturn new ArrayValidator(validator);\n\t}\n\n\tpublic typedArray<T extends TypedArray>(type: TypedArrayName = 'TypedArray') {\n\t\treturn new TypedArrayValidator<T>(type);\n\t}\n\n\tpublic get int8Array() {\n\t\treturn this.typedArray<Int8Array>('Int8Array');\n\t}\n\n\tpublic get uint8Array() {\n\t\treturn this.typedArray<Uint8Array>('Uint8Array');\n\t}\n\n\tpublic get uint8ClampedArray() {\n\t\treturn this.typedArray<Uint8ClampedArray>('Uint8ClampedArray');\n\t}\n\n\tpublic get int16Array() {\n\t\treturn this.typedArray<Int16Array>('Int16Array');\n\t}\n\n\tpublic get uint16Array() {\n\t\treturn this.typedArray<Uint16Array>('Uint16Array');\n\t}\n\n\tpublic get int32Array() {\n\t\treturn this.typedArray<Int32Array>('Int32Array');\n\t}\n\n\tpublic get uint32Array() {\n\t\treturn this.typedArray<Uint32Array>('Uint32Array');\n\t}\n\n\tpublic get float32Array() {\n\t\treturn this.typedArray<Float32Array>('Float32Array');\n\t}\n\n\tpublic get float64Array() {\n\t\treturn this.typedArray<Float64Array>('Float64Array');\n\t}\n\n\tpublic get bigInt64Array() {\n\t\treturn this.typedArray<BigInt64Array>('BigInt64Array');\n\t}\n\n\tpublic get bigUint64Array() {\n\t\treturn this.typedArray<BigUint64Array>('BigUint64Array');\n\t}\n\n\tpublic tuple<T extends [...BaseValidator<any>[]]>(validators: [...T]): TupleValidator<UnwrapTuple<T>> {\n\t\treturn new TupleValidator(validators);\n\t}\n\n\tpublic set<T>(validator: BaseValidator<T>) {\n\t\treturn new SetValidator(validator);\n\t}\n\n\tpublic record<T>(validator: BaseValidator<T>) {\n\t\treturn new RecordValidator(validator);\n\t}\n\n\tpublic map<T, U>(keyValidator: BaseValidator<T>, valueValidator: BaseValidator<U>) {\n\t\treturn new MapValidator(keyValidator, valueValidator);\n\t}\n\n\tpublic lazy<T extends BaseValidator<unknown>>(validator: (value: unknown) => T) {\n\t\treturn new LazyValidator(validator);\n\t}\n}\n", "import { Shapes } from './lib/Shapes';\n\nexport const s = new Shapes();\n\nexport * from './lib/Result';\nexport * from './lib/configs';\nexport * from './lib/errors/BaseError';\nexport * from './lib/errors/CombinedError';\nexport * from './lib/errors/CombinedPropertyError';\nexport * from './lib/errors/ExpectedConstraintError';\nexport * from './lib/errors/ExpectedValidationError';\nexport * from './lib/errors/MissingPropertyError';\nexport * from './lib/errors/MultiplePossibilitiesConstraintError';\nexport * from './lib/errors/UnknownEnumValueError';\nexport * from './lib/errors/UnknownPropertyError';\nexport * from './lib/errors/ValidationError';\nexport * from './type-exports';\n"]}