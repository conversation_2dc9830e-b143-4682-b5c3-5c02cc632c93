# 🚀 QUICK START GUIDE

## What You Have
✅ **Outpost Website Template** - Professional Rust gaming community website  
✅ **OutpostLink System** - Discord + Steam account linking with role management  
✅ **Discord Bot** - Automatic role assignment and sync commands  
✅ **Rust Plugin** - In-game account verification and group management  

## 🔧 What You Need Before Starting

### Required Accounts & Keys
- [ ] **Steam API Key** → Get at https://steamcommunity.com/dev/apikey
- [ ] **Your Steam ID** → Find at https://steamid.io/
- [ ] **Discord Application** → Create at https://discord.com/developers/applications
- [ ] **Web Hosting** → PHP 8.1+ with MySQL (or local XAMPP)
- [ ] **Domain Name** → For production use

### Required Software
- [ ] **XAMPP/WAMP** → For local development
- [ ] **Node.js 16+** → For Discord bot
- [ ] **Text Editor** → VS Code, Notepad++, etc.

## ⚡ 5-Minute Setup

### Step 1: Edit Configuration
1. Open `configure.php` in a text editor
2. Replace ALL `YOUR_*` values with your actual credentials:
   ```php
   'steam_api_key' => 'ABC123...',        // From Steam
   'admin_steam_id' => '********...',     // Your Steam ID
   'discord_client_id' => '123456...',    // From Discord App
   'discord_bot_token' => 'MTIz...',      // From Discord Bot
   // ... etc
   ```

### Step 2: Run Auto-Configuration
```bash
php configure.php
```

### Step 3: Set Up Database
1. Start XAMPP/WAMP
2. Open phpMyAdmin
3. Import `database_setup.sql`

### Step 4: Deploy Website
1. Copy `outpost-website-template-v1.1.1/*` to web root
2. Copy `outpost-template-outpostlink-v1.1.2/web/*` to web root
3. Visit `http://localhost/compatibility.php`

### Step 5: Start Discord Bot
```bash
cd outpost-template-outpostlink-v1.1.2/bot/
npm install
node index.js
```

## 🎯 Testing Checklist

### Website Tests
- [ ] Visit `http://localhost/` - Website loads
- [ ] Click Steam login - Authentication works
- [ ] Access Web Panel from footer - Admin panel accessible
- [ ] Visit `/link.php` - Account linking page loads

### Discord Bot Tests
- [ ] Bot appears online in Discord
- [ ] `/sync` command works
- [ ] Role assignment functions

### Integration Tests
- [ ] Link Steam + Discord accounts
- [ ] Verify roles are assigned
- [ ] Test Rust server plugin (if applicable)

## 🚨 Common Issues & Fixes

### "Steam API Key Error"
- ❌ **Problem**: Red error about missing API key
- ✅ **Fix**: Edit `steamauth/SteamConfig.php`, add your Steam API key

### "Access Denied" to Web Panel
- ❌ **Problem**: Can't access admin panel
- ✅ **Fix**: Edit `admin/config.php`, add your Steam ID to admins array

### Bot Won't Start
- ❌ **Problem**: Bot crashes or won't connect
- ✅ **Fix**: Check `botCon.json` has correct token and client ID

### Database Connection Failed
- ❌ **Problem**: Can't connect to database
- ✅ **Fix**: Verify MySQL is running, check credentials in `link/config.php`

### "Installation Required" Message
- ❌ **Problem**: OutpostLink shows installation page
- ✅ **Fix**: Complete web panel installation or ensure `link/config.php` exists

## 📁 File Structure Overview

```
📁 Your Web Root/
├── 📄 index.php              # Main website
├── 📄 link.php               # Account linking page
├── 📄 admin.php              # Admin panel
├── 📁 steamauth/             # Steam authentication
├── 📁 link/                  # OutpostLink system
├── 📁 templates/             # Website templates
└── 📁 assets/                # CSS, JS, images

📁 Bot Files/
├── 📄 index.js               # Bot main file
├── 📄 botCon.json           # Bot configuration
├── 📄 package.json          # Dependencies
└── 📁 commands/             # Bot commands

📁 Rust Server/
├── 📄 OutpostLink.cs        # Plugin file
└── 📄 OutpostLink.json      # Plugin config
```

## 🔗 Important URLs

- **Website**: `http://localhost/`
- **Account Linking**: `http://localhost/link.php`
- **Admin Panel**: `http://localhost/admin.php`
- **Compatibility Check**: `http://localhost/compatibility.php`

## 📞 Need Help?

1. Check the detailed `setup_guide.md`
2. Verify all configuration files have correct values
3. Check PHP/MySQL error logs
4. Ensure all required services are running

## 🎉 You're All Set!

Once everything is working:
1. Customize your website design and content
2. Configure Discord roles and permissions
3. Set up your Rust server plugin
4. Deploy to production hosting
5. Enjoy your professional gaming community platform!
