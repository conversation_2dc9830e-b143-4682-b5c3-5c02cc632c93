{"name": "@sapphire/async-queue", "version": "1.5.0", "description": "Sequential asynchronous lock-based queue for promises", "author": "@sapphire", "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "browser": "dist/index.global.js", "unpkg": "dist/index.global.js", "types": "dist/index.d.ts", "exports": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "sideEffects": false, "homepage": "https://github.com/sapphiredev/utilities/tree/main/packages/async-queue", "scripts": {"test": "vitest run", "lint": "eslint src tests --ext ts --fix -c ../../.eslintrc", "build": "tsup", "prepack": "yarn build", "bump": "cliff-jumper", "check-update": "cliff-jumper --dry-run"}, "repository": {"type": "git", "url": "git+https://github.com/sapphiredev/utilities.git", "directory": "packages/async-queue"}, "files": ["dist/**/*.js*", "dist/**/*.mjs*", "dist/**/*.d*"], "engines": {"node": ">=v14.0.0", "npm": ">=7.0.0"}, "keywords": ["@sapphire/async-queue", "bot", "typescript", "ts", "yarn", "discord", "sapphire", "standalone"], "bugs": {"url": "https://github.com/sapphiredev/utilities/issues"}, "publishConfig": {"access": "public"}, "devDependencies": {"@favware/cliff-jumper": "^1.8.6", "@vitest/coverage-c8": "^0.22.0", "c8": "^7.12.0", "tsup": "^6.2.2", "typescript": "^4.7.4", "vitest": "^0.22.0"}}