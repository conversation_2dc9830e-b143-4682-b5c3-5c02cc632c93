{"name": "@keyv/mysql", "version": "1.7.1", "description": "MySQL/MariaDB storage adapter for Keyv", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --project tsconfig.dist.json", "prepare": "yarn build", "test": "xo && c8 ava --serial", "test:ci": "xo && ava --serial", "clean": "rm -rf node_modules && rm -rf ./coverage"}, "xo": {"rules": {"unicorn/prefer-module": 0, "unicorn/prefer-event-target": 0, "unicorn/prefer-string-replace-all": 0, "unicorn/prefer-node-protocol": 0, "import/extensions": 0, "@typescript-eslint/no-unsafe-return": 0, "@typescript-eslint/no-unsafe-assignment": 0, "@typescript-eslint/no-unsafe-call": 0, "@typescript-eslint/no-confusing-void-expression": 0, "@typescript-eslint/no-dynamic-delete": 0, "ava/no-ignored-test-files": ["error", {"extensions": ["js", "ts"]}]}}, "ava": {"require": ["requirable", "ts-node/register"], "extensions": ["js", "ts"]}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "keywords": ["mysql", "ma<PERSON>b", "sql", "keyv", "storage", "adapter", "key", "value", "store", "cache", "ttl"], "author": "<PERSON> <<EMAIL>> (http://jaredwray.com)", "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "homepage": "https://github.com/jaredwray/keyv", "dependencies": {"mysql2": "3.6.3"}, "devDependencies": {"@keyv/test-suite": "*", "keyv": "*", "requirable": "^1.0.5", "ts-node": "^10.9.1", "tsd": "^0.29.0"}, "tsd": {"directory": "test"}, "engines": {"node": ">= 14"}, "files": ["dist"]}