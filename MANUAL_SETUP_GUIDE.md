# 🛠️ Manual Local Setup Guide - Guaranteed to Work!

Since the automated setup had issues, here's a step-by-step manual guide that will definitely get you running:

## 🎯 Goal
Set up a local test environment for your Rust website with OutpostLink on Windows.

## 📋 What We'll Install
- ✅ XAMPP (Apache + PHP + MySQL)
- ✅ Your website files
- ✅ Node.js (already installed)
- ✅ Discord bot

---

## 🚀 Step 1: Fix XAMPP Installation

### Option A: Use XAMPP Control Panel (Easiest)

1. **Open File Explorer** → Navigate to `C:\xampp\`
2. **Right-click `xampp-control.exe`** → **"Run as administrator"**
3. **Click "Start" next to Apache**
4. **Click "Start" next to MySQL**
5. **Wait for both to show green "Running" status**

### Option B: If XAMPP Control Panel doesn't work

1. **Download fresh XAMPP**:
   - Go to https://www.apachefriends.org/download.html
   - Download XAMPP for Windows (PHP 8.2)
   - Install to `C:\xampp\`

2. **Start services manually**:
   ```cmd
   # Open Command Prompt as Administrator
   cd C:\xampp
   apache_start.bat
   mysql_start.bat
   ```

---

## 🌐 Step 2: Test XAMPP

1. **Open browser** → Go to `http://localhost`
2. **You should see**: XAMPP Dashboard
3. **If you see an error**: XAMPP isn't running properly

### Troubleshooting XAMPP
- **Port 80 in use**: Change Apache port to 8080 in `C:\xampp\apache\conf\httpd.conf`
- **Permission issues**: Run as administrator
- **Antivirus blocking**: Add XAMPP folder to antivirus exclusions

---

## 📁 Step 3: Copy Website Files

### Method 1: Using File Explorer
1. **Open two File Explorer windows**:
   - Window 1: `F:\Rust_website\outpost-website-template-v1.1.1\`
   - Window 2: `C:\xampp\htdocs\`

2. **Copy all files** from Window 1 to Window 2

3. **Repeat for OutpostLink**:
   - From: `F:\Rust_website\outpost-template-outpostlink-v1.1.2\web\`
   - To: `C:\xampp\htdocs\`

### Method 2: Using PowerShell
```powershell
# Run in PowerShell as Administrator
Copy-Item -Path "F:\Rust_website\outpost-website-template-v1.1.1\*" -Destination "C:\xampp\htdocs\" -Recurse -Force
Copy-Item -Path "F:\Rust_website\outpost-template-outpostlink-v1.1.2\web\*" -Destination "C:\xampp\htdocs\" -Recurse -Force
```

---

## ⚙️ Step 4: Basic Configuration

### 4.1 Configure Steam Authentication
1. **Get Steam API Key**: https://steamcommunity.com/dev/apikey
2. **Edit file**: `C:\xampp\htdocs\steamauth\SteamConfig.php`
3. **Replace**: `YOUR_STEAM_API_KEY_HERE` with your actual key

### 4.2 Configure Admin Access
1. **Get your Steam ID**: https://steamid.io/
2. **Edit file**: `C:\xampp\htdocs\admin\config.php`
3. **Replace**: `YOUR_STEAM_ID_HERE` with your Steam ID

---

## 🧪 Step 5: Test Your Website

### Basic Tests
1. **Main website**: http://localhost/
2. **Compatibility check**: http://localhost/compatibility.php
3. **Admin panel**: http://localhost/admin.php
4. **Account linking**: http://localhost/link.php

### Expected Results
- ✅ **Main website**: Should load with server info
- ✅ **Compatibility**: Should show green checkmarks
- ✅ **Admin panel**: Should allow Steam login
- ⚠️ **Account linking**: May show "installation required" (normal)

---

## 🗄️ Step 6: Set Up Database

### 6.1 Access phpMyAdmin
1. **Go to**: http://localhost/phpmyadmin/
2. **Username**: `root`
3. **Password**: (leave empty)

### 6.2 Create Database
1. **Click "New"** in left sidebar
2. **Database name**: `outpostlink`
3. **Click "Create"**

### 6.3 Import Database Structure
1. **Click on `outpostlink` database**
2. **Click "Import" tab**
3. **Choose file**: `F:\Rust_website\database_setup.sql`
4. **Click "Go"**

---

## 🤖 Step 7: Set Up Discord Bot

### 7.1 Install Bot Dependencies
```cmd
cd F:\Rust_website\outpost-template-outpostlink-v1.1.2\bot\
npm install
```

### 7.2 Configure Bot
1. **Edit**: `F:\Rust_website\outpost-template-outpostlink-v1.1.2\bot\botCon.json`
2. **Add your Discord bot token and settings**

### 7.3 Start Bot
```cmd
cd F:\Rust_website\outpost-template-outpostlink-v1.1.2\bot\
node index.js
```

---

## 🎉 Step 8: Final Testing

### Test Checklist
- [ ] XAMPP services running (Apache + MySQL)
- [ ] Website loads at http://localhost/
- [ ] Steam login works
- [ ] Admin panel accessible
- [ ] Database connection works
- [ ] Discord bot connects

---

## 🚨 Common Issues & Solutions

### "Steam API Key Error"
- **Problem**: Red error about missing API key
- **Solution**: Edit `steamauth\SteamConfig.php` with your Steam API key

### "Can't access admin panel"
- **Problem**: Access denied
- **Solution**: Add your Steam ID to `admin\config.php`

### "Database connection failed"
- **Problem**: Can't connect to MySQL
- **Solution**: Ensure MySQL is running in XAMPP

### "Port 80 already in use"
- **Problem**: Apache won't start
- **Solution**: Stop other web servers or change Apache port

---

## 📞 Need Help?

If you're still having issues:

1. **Check XAMPP logs**: `C:\xampp\apache\logs\error.log`
2. **Verify file permissions**: Ensure files are readable
3. **Check Windows Firewall**: Allow Apache and MySQL
4. **Try different ports**: Use 8080 instead of 80

---

## 🎯 Quick Success Path

**If you just want to see the website working quickly:**

1. **Start XAMPP Control Panel** (run as admin)
2. **Start Apache + MySQL**
3. **Copy website files** to `C:\xampp\htdocs\`
4. **Visit** http://localhost/
5. **Add Steam API key** when ready

**That's it!** You'll have a working local copy of your Rust website.
