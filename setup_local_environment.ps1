# Local Development Environment Setup Script for Windows
# This script will set up a complete PHP + MySQL environment for your Rust website

Write-Host "=== Setting up Local Development Environment ===" -ForegroundColor Green

# Create a local development directory
$devPath = "C:\RustWebsite_Local"
Write-Host "Creating development directory at $devPath" -ForegroundColor Yellow
New-Item -ItemType Directory -Path $devPath -Force | Out-Null

# Download and extract portable PHP
Write-Host "Downloading PHP 8.2..." -ForegroundColor Yellow
$phpUrl = "https://windows.php.net/downloads/releases/php-8.2.25-Win32-vs16-x64.zip"
$phpZip = "$devPath\php.zip"
$phpPath = "$devPath\php"

try {
    Invoke-WebRequest -Uri $phpUrl -OutFile $phpZip -UseBasicParsing
    Expand-Archive -Path $phpZip -DestinationPath $phpPath -Force
    Remove-Item $phpZip
    Write-Host "✅ PHP downloaded and extracted" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to download PHP: $($_.Exception.Message)" -ForegroundColor Red
}

# Download and extract portable MySQL
Write-Host "Downloading MySQL..." -ForegroundColor Yellow
$mysqlUrl = "https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.39-winx64.zip"
$mysqlZip = "$devPath\mysql.zip"
$mysqlPath = "$devPath\mysql"

try {
    Invoke-WebRequest -Uri $mysqlUrl -OutFile $mysqlZip -UseBasicParsing
    Expand-Archive -Path $mysqlZip -DestinationPath $mysqlPath -Force
    Remove-Item $mysqlZip
    Write-Host "✅ MySQL downloaded and extracted" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to download MySQL: $($_.Exception.Message)" -ForegroundColor Red
}

# Create PHP configuration
Write-Host "Configuring PHP..." -ForegroundColor Yellow
$phpIni = @"
; PHP Configuration for Rust Website
extension_dir = "ext"
extension=curl
extension=fileinfo
extension=mbstring
extension=mysqli
extension=pdo_mysql
extension=openssl
extension=gd
extension=zip

allow_url_fopen = On
memory_limit = 256M
upload_max_filesize = 64M
post_max_size = 64M
max_execution_time = 300

date.timezone = "UTC"
"@

$phpIni | Out-File -FilePath "$phpPath\php.ini" -Encoding UTF8
Write-Host "✅ PHP configured" -ForegroundColor Green

# Create web directory
$webPath = "$devPath\www"
New-Item -ItemType Directory -Path $webPath -Force | Out-Null

# Copy website files
Write-Host "Setting up website files..." -ForegroundColor Yellow
$sourceWebsite = "outpost-website-template-v1.1.1"
$sourceOutpostLink = "outpost-template-outpostlink-v1.1.2\web"

if (Test-Path $sourceWebsite) {
    Copy-Item -Path "$sourceWebsite\*" -Destination $webPath -Recurse -Force
    Write-Host "✅ Website template copied" -ForegroundColor Green
} else {
    Write-Host "❌ Website template not found at $sourceWebsite" -ForegroundColor Red
}

if (Test-Path $sourceOutpostLink) {
    Copy-Item -Path "$sourceOutpostLink\*" -Destination $webPath -Recurse -Force
    Write-Host "✅ OutpostLink files copied" -ForegroundColor Green
} else {
    Write-Host "❌ OutpostLink files not found at $sourceOutpostLink" -ForegroundColor Red
}

# Create startup scripts
Write-Host "Creating startup scripts..." -ForegroundColor Yellow

# PHP Server startup script
$startPhp = @"
@echo off
echo Starting PHP Development Server...
cd /d "$webPath"
"$phpPath\php.exe" -S localhost:8080 -t .
pause
"@
$startPhp | Out-File -FilePath "$devPath\start_php_server.bat" -Encoding ASCII

# MySQL startup script
$startMysql = @"
@echo off
echo Starting MySQL Server...
cd /d "$mysqlPath\mysql-8.0.39-winx64\bin"
mysqld.exe --console --datadir="$devPath\mysql_data"
pause
"@
$startMysql | Out-File -FilePath "$devPath\start_mysql.bat" -Encoding ASCII

# Combined startup script
$startAll = @"
@echo off
echo Starting Local Development Environment...
echo.
echo Starting MySQL in background...
start "MySQL" "$devPath\start_mysql.bat"
echo.
echo Waiting 5 seconds for MySQL to start...
timeout /t 5 /nobreak > nul
echo.
echo Starting PHP Server...
echo Website will be available at: http://localhost:8080
echo.
start "PHP Server" "$devPath\start_php_server.bat"
echo.
echo Both services started!
echo MySQL: Background process
echo PHP: http://localhost:8080
echo.
pause
"@
$startAll | Out-File -FilePath "$devPath\START_DEVELOPMENT_SERVER.bat" -Encoding ASCII

Write-Host "✅ Startup scripts created" -ForegroundColor Green

# Create a simple test page
$testPage = @"
<?php
echo "<h1>🎉 Local Development Environment Working!</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";
echo "<h2>Next Steps:</h2>";
echo "<ul>";
echo "<li><a href='compatibility.php'>Check Compatibility</a></li>";
echo "<li><a href='index.php'>Main Website</a></li>";
echo "<li><a href='admin.php'>Admin Panel</a></li>";
echo "<li><a href='link.php'>Account Linking</a></li>";
echo "</ul>";
?>
"@
$testPage | Out-File -FilePath "$webPath\test.php" -Encoding UTF8

Write-Host ""
Write-Host "=== Setup Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "📁 Development folder: $devPath" -ForegroundColor Cyan
Write-Host "🌐 Website files: $webPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "🚀 To start the development server:" -ForegroundColor Yellow
Write-Host "   Double-click: $devPath\START_DEVELOPMENT_SERVER.bat" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Once started, visit:" -ForegroundColor Yellow
Write-Host "   http://localhost:8080/test.php" -ForegroundColor White
Write-Host ""
Write-Host "⚙️  Next steps:" -ForegroundColor Yellow
Write-Host "   1. Start the development server" -ForegroundColor White
Write-Host "   2. Configure your Steam API key" -ForegroundColor White
Write-Host "   3. Set up your admin Steam ID" -ForegroundColor White
Write-Host "   4. Test the website functionality" -ForegroundColor White
Write-Host ""
