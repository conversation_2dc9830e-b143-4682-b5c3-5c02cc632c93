# Simple Local Configuration Script
Write-Host "=== Rust Website Local Setup ===" -ForegroundColor Green

# Check XAMPP
$xamppPath = "C:\xampp"
if (Test-Path $xamppPath) {
    Write-Host "✅ XAMPP found" -ForegroundColor Green
} else {
    Write-Host "❌ XAMPP not found. Please install from https://www.apachefriends.org/" -ForegroundColor Red
    exit
}

# Copy files
Write-Host "Copying website files..." -ForegroundColor Yellow
try {
    Copy-Item -Path "outpost-website-template-v1.1.1\*" -Destination "C:\xampp\htdocs\" -Recurse -Force
    Copy-Item -Path "outpost-template-outpostlink-v1.1.2\web\*" -Destination "C:\xampp\htdocs\" -Recurse -Force
    Write-Host "✅ Files copied successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to copy files" -ForegroundColor Red
}

# Create test page
$testContent = @'
<!DOCTYPE html>
<html>
<head>
    <title>Local Rust Website Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        ul { list-style-type: none; }
        li { margin: 10px 0; }
        a { text-decoration: none; color: #0066cc; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>🎉 Local Rust Website Test</h1>
    <p><strong>Status:</strong> <span class="success">Ready for testing!</span></p>
    
    <h2>🔗 Quick Links:</h2>
    <ul>
        <li>🏠 <a href="index.php">Main Website</a></li>
        <li>⚙️ <a href="compatibility.php">Compatibility Check</a></li>
        <li>👤 <a href="admin.php">Admin Panel</a></li>
        <li>🔗 <a href="link.php">Account Linking</a></li>
        <li>🗄️ <a href="phpmyadmin/">Database (phpMyAdmin)</a></li>
    </ul>
    
    <h2>📋 Next Steps:</h2>
    <ol>
        <li>Configure Steam API key in <code>steamauth/SteamConfig.php</code></li>
        <li>Set your Steam ID in <code>admin/config.php</code></li>
        <li>Test the main website functionality</li>
        <li>Set up Discord bot if needed</li>
    </ol>
    
    <h2>🚨 Troubleshooting:</h2>
    <ul>
        <li>If pages don't load: Check that Apache is running in XAMPP</li>
        <li>If database errors: Check that MySQL is running in XAMPP</li>
        <li>If Steam login fails: Add your Steam API key</li>
        <li>If admin access denied: Add your Steam ID to admin config</li>
    </ul>
</body>
</html>
'@

$testContent | Out-File -FilePath "C:\xampp\htdocs\test.html" -Encoding UTF8
Write-Host "✅ Test page created" -ForegroundColor Green

# Start XAMPP
Write-Host ""
Write-Host "Starting XAMPP Control Panel..." -ForegroundColor Yellow
Start-Process "C:\xampp\xampp-control.exe" -Verb RunAs

Write-Host ""
Write-Host "=== Setup Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. In XAMPP Control Panel: Start Apache and MySQL" -ForegroundColor White
Write-Host "2. Visit: http://localhost/test.html" -ForegroundColor White
Write-Host "3. Configure Steam API key and admin Steam ID" -ForegroundColor White
Write-Host "4. Test your website!" -ForegroundColor White
Write-Host ""
