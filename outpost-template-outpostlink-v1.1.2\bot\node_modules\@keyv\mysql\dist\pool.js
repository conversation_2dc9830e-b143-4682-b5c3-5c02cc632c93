"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.endPool = exports.pool = exports.parseConnectionString = void 0;
const mysql2_1 = __importDefault(require("mysql2"));
let mysqlPool;
let globalUri;
const parseConnectionString = (connectionString) => {
    // Handle # character as URL breaks when it is present
    connectionString = connectionString.replace(/#/g, '%23');
    // Create a new URL object
    const url = new URL(connectionString);
    // Create the poolOptions object
    const poolOptions = {
        user: decodeURIComponent(url.username),
        password: decodeURIComponent(url.password) || undefined,
        host: url.hostname,
        port: url.port ? Number.parseInt(url.port, 10) : undefined,
        database: decodeURIComponent(url.pathname.slice(1)), // Remove the leading '/'
    };
    // Remove undefined properties
    for (const key of Object.keys(poolOptions)) {
        // @ts-expect-error - poolOptions
        if (poolOptions[key] === undefined) {
            //  @ts-expect-error - @typescript-eslint/no-dynamic-delete
            delete poolOptions[key];
        }
    }
    return poolOptions;
};
exports.parseConnectionString = parseConnectionString;
const pool = (uri, options = {}) => {
    if (globalUri !== uri) {
        mysqlPool = undefined;
        globalUri = uri;
    }
    const connectObject = (0, exports.parseConnectionString)(uri);
    const poolOptions = Object.assign(Object.assign({}, connectObject), options);
    mysqlPool = mysqlPool !== null && mysqlPool !== void 0 ? mysqlPool : mysql2_1.default.createPool(poolOptions);
    return mysqlPool.promise();
};
exports.pool = pool;
const endPool = () => {
    if (mysqlPool) {
        mysqlPool.end();
    }
    globalUri = undefined;
};
exports.endPool = endPool;
//# sourceMappingURL=pool.js.map