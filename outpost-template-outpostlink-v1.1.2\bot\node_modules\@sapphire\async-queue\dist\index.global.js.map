{"version": 3, "sources": ["../src/index.ts", "../src/lib/AsyncQueueEntry.ts", "../src/lib/AsyncQueue.ts"], "sourcesContent": ["export * from './lib/AsyncQueue';\n", "import type { AsyncQueue } from './AsyncQueue';\n\n/**\n * @internal\n */\nexport class AsyncQueueEntry {\n\tpublic readonly promise: Promise<void>;\n\tprivate resolve!: () => void;\n\tprivate reject!: (error: Error) => void;\n\tprivate readonly queue: AsyncQueue;\n\tprivate signal: PolyFillAbortSignal | null = null;\n\tprivate signalListener: (() => void) | null = null;\n\n\tpublic constructor(queue: AsyncQueue) {\n\t\tthis.queue = queue;\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n\n\tpublic setSignal(signal: AbortSignal) {\n\t\tif (signal.aborted) return this;\n\n\t\tthis.signal = signal as PolyFillAbortSignal;\n\t\tthis.signalListener = () => {\n\t\t\tconst index = this.queue['promises'].indexOf(this);\n\t\t\tif (index !== -1) this.queue['promises'].splice(index, 1);\n\n\t\t\tthis.reject(new Error('Request aborted manually'));\n\t\t};\n\t\tthis.signal.addEventListener('abort', this.signalListener);\n\t\treturn this;\n\t}\n\n\tpublic use() {\n\t\tthis.dispose();\n\t\tthis.resolve();\n\t\treturn this;\n\t}\n\n\tpublic abort() {\n\t\tthis.dispose();\n\t\tthis.reject(new Error('Request aborted manually'));\n\t\treturn this;\n\t}\n\n\tprivate dispose() {\n\t\tif (this.signal) {\n\t\t\tthis.signal.removeEventListener('abort', this.signalListener!);\n\t\t\tthis.signal = null;\n\t\t\tthis.signalListener = null;\n\t\t}\n\t}\n}\n\ninterface PolyFillAbortSignal {\n\treadonly aborted: boolean;\n\taddEventListener(type: 'abort', listener: () => void): void;\n\tremoveEventListener(type: 'abort', listener: () => void): void;\n}\n", "import { AsyncQueueEntry } from './AsyncQueueEntry';\n\n/**\n * The AsyncQueue class used to sequentialize burst requests\n */\nexport class AsyncQueue {\n\t/**\n\t * The amount of entries in the queue, including the head.\n\t * @seealso {@link queued} for the queued count.\n\t */\n\tpublic get remaining(): number {\n\t\treturn this.promises.length;\n\t}\n\n\t/**\n\t * The amount of queued entries.\n\t * @seealso {@link remaining} for the count with the head.\n\t */\n\tpublic get queued(): number {\n\t\treturn this.remaining === 0 ? 0 : this.remaining - 1;\n\t}\n\n\t/**\n\t * The promises array\n\t */\n\tprivate promises: AsyncQueueEntry[] = [];\n\n\t/**\n\t * Waits for last promise and queues a new one\n\t * @example\n\t * ```typescript\n\t * const queue = new AsyncQueue();\n\t * async function request(url, options) {\n\t *     await queue.wait({ signal: options.signal });\n\t *     try {\n\t *         const result = await fetch(url, options);\n\t *         // Do some operations with 'result'\n\t *     } finally {\n\t *         // Remove first entry from the queue and resolve for the next entry\n\t *         queue.shift();\n\t *     }\n\t * }\n\t *\n\t * request(someUrl1, someOptions1); // Will call fetch() immediately\n\t * request(someUrl2, someOptions2); // Will call fetch() after the first finished\n\t * request(someUrl3, someOptions3); // Will call fetch() after the second finished\n\t * ```\n\t */\n\tpublic wait(options?: Readonly<AsyncQueueWaitOptions>): Promise<void> {\n\t\tconst entry = new AsyncQueueEntry(this);\n\n\t\tif (this.promises.length === 0) {\n\t\t\tthis.promises.push(entry);\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\tthis.promises.push(entry);\n\t\tif (options?.signal) entry.setSignal(options.signal);\n\t\treturn entry.promise;\n\t}\n\n\t/**\n\t * Unlocks the head lock and transfers the next lock (if any) to the head.\n\t */\n\tpublic shift(): void {\n\t\tif (this.promises.length === 0) return;\n\t\tif (this.promises.length === 1) {\n\t\t\t// Remove the head entry.\n\t\t\tthis.promises.shift();\n\t\t\treturn;\n\t\t}\n\n\t\t// Remove the head entry, making the 2nd entry the new one.\n\t\t// Then use the head entry, which will unlock the promise.\n\t\tthis.promises.shift();\n\t\tthis.promises[0].use();\n\t}\n\n\t/**\n\t * Aborts all the pending promises.\n\t * @note To avoid race conditions, this does **not** unlock the head lock.\n\t */\n\tpublic abortAll(): void {\n\t\t// If there are no queued entries, skip early.\n\t\tif (this.queued === 0) return;\n\n\t\t// Abort all the entries except the head, that is why the loop starts at\n\t\t// 1 and not at 0.\n\t\tfor (let i = 1; i < this.promises.length; ++i) {\n\t\t\tthis.promises[i].abort();\n\t\t}\n\n\t\tthis.promises.length = 1;\n\t}\n}\n\nexport interface AsyncQueueWaitOptions {\n\tsignal?: AbortSignal | undefined | null;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;;;ACKO,MAAM,kBAAN,MAAsB;AAAA,IAQrB,YAAY,OAAmB;AAPtC,0BAAgB;AAChB,0BAAQ;AACR,0BAAQ;AACR,0BAAiB;AACjB,0BAAQ,UAAqC;AAC7C,0BAAQ,kBAAsC;AAG7C,WAAK,QAAQ;AACb,WAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,aAAK,UAAU;AACf,aAAK,SAAS;AAAA,MACf,CAAC;AAAA,IACF;AAAA,IAEO,UAAU,QAAqB;AACrC,UAAI,OAAO;AAAS,eAAO;AAE3B,WAAK,SAAS;AACd,WAAK,iBAAiB,MAAM;AAC3B,cAAM,QAAQ,KAAK,MAAM,YAAY,QAAQ,IAAI;AACjD,YAAI,UAAU;AAAI,eAAK,MAAM,YAAY,OAAO,OAAO,CAAC;AAExD,aAAK,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,MAClD;AACA,WAAK,OAAO,iBAAiB,SAAS,KAAK,cAAc;AACzD,aAAO;AAAA,IACR;AAAA,IAEO,MAAM;AACZ,WAAK,QAAQ;AACb,WAAK,QAAQ;AACb,aAAO;AAAA,IACR;AAAA,IAEO,QAAQ;AACd,WAAK,QAAQ;AACb,WAAK,OAAO,IAAI,MAAM,0BAA0B,CAAC;AACjD,aAAO;AAAA,IACR;AAAA,IAEQ,UAAU;AACjB,UAAI,KAAK,QAAQ;AAChB,aAAK,OAAO,oBAAoB,SAAS,KAAK,cAAe;AAC7D,aAAK,SAAS;AACd,aAAK,iBAAiB;AAAA,MACvB;AAAA,IACD;AAAA,EACD;AAjDa;;;ACAN,MAAM,aAAN,MAAiB;AAAA,IAAjB;AAoBN,0BAAQ,YAA8B,CAAC;AAAA;AAAA,IAfvC,IAAW,YAAoB;AAC9B,aAAO,KAAK,SAAS;AAAA,IACtB;AAAA,IAMA,IAAW,SAAiB;AAC3B,aAAO,KAAK,cAAc,IAAI,IAAI,KAAK,YAAY;AAAA,IACpD;AAAA,IA4BO,KAAK,SAA0D;AACrE,YAAM,QAAQ,IAAI,gBAAgB,IAAI;AAEtC,UAAI,KAAK,SAAS,WAAW,GAAG;AAC/B,aAAK,SAAS,KAAK,KAAK;AACxB,eAAO,QAAQ,QAAQ;AAAA,MACxB;AAEA,WAAK,SAAS,KAAK,KAAK;AACxB,UAAI,SAAS;AAAQ,cAAM,UAAU,QAAQ,MAAM;AACnD,aAAO,MAAM;AAAA,IACd;AAAA,IAKO,QAAc;AACpB,UAAI,KAAK,SAAS,WAAW;AAAG;AAChC,UAAI,KAAK,SAAS,WAAW,GAAG;AAE/B,aAAK,SAAS,MAAM;AACpB;AAAA,MACD;AAIA,WAAK,SAAS,MAAM;AACpB,WAAK,SAAS,GAAG,IAAI;AAAA,IACtB;AAAA,IAMO,WAAiB;AAEvB,UAAI,KAAK,WAAW;AAAG;AAIvB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,EAAE,GAAG;AAC9C,aAAK,SAAS,GAAG,MAAM;AAAA,MACxB;AAEA,WAAK,SAAS,SAAS;AAAA,IACxB;AAAA,EACD;AAzFa;", "names": []}