/// <reference types="node" />
import EventEmitter from 'events';
import mysql from 'mysql2';
import type { KeyvStoreAdapter, StoredData } from 'keyv';
import { type KeyvMysqlOptions } from './types';
type QueryType<T> = Promise<T extends mysql.RowDataPacket[][] | mysql.RowDataPacket[] | mysql.OkPacket | mysql.OkPacket[] | mysql.ResultSetHeader ? T : never>;
declare class KeyvMysql extends EventEmitter implements KeyvStoreAdapter {
    ttlSupport: boolean;
    opts: KeyvMysqlOptions;
    namespace?: string;
    query: <T>(sqlString: string) => QueryType<T>;
    constructor(keyvOptions?: KeyvMysqlOptions | string);
    get<Value>(key: string): Promise<StoredData<Value>>;
    getMany<Value>(keys: string[]): Promise<StoredData<Value>[]>;
    set(key: string, value: any): Promise<never>;
    delete(key: string): Promise<boolean>;
    deleteMany(key: string[]): Promise<boolean>;
    clear(): Promise<void>;
    iterator(namespace?: string): AsyncGenerator<any, void, any>;
    has(key: string): Promise<boolean>;
    disconnect(): Promise<void>;
}
export = KeyvMysql;
