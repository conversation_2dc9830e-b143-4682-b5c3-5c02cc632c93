@echo off
chcp 65001 >nul
echo ========================================
echo    Rust Website Local Setup
echo ========================================
echo.

echo Checking XAMPP installation...
if not exist "C:\xampp\xampp-control.exe" (
    echo ❌ XAMPP not found!
    echo Please install XAMPP from https://www.apachefriends.org/
    pause
    exit /b 1
)
echo ✅ XAMPP found

echo.
echo Copying website files to XAMPP...
xcopy /E /I /Y "outpost-website-template-v1.1.1\*" "C:\xampp\htdocs\" >nul 2>&1
xcopy /E /I /Y "outpost-template-outpostlink-v1.1.2\web\*" "C:\xampp\htdocs\" >nul 2>&1
echo ✅ Website files copied

echo.
echo Creating test page...
echo ^<!DOCTYPE html^> > "C:\xampp\htdocs\test.html"
echo ^<html^> >> "C:\xampp\htdocs\test.html"
echo ^<head^> >> "C:\xampp\htdocs\test.html"
echo ^<title^>Local Rust Website Test^</title^> >> "C:\xampp\htdocs\test.html"
echo ^<style^> >> "C:\xampp\htdocs\test.html"
echo body { font-family: Arial, sans-serif; margin: 40px; } >> "C:\xampp\htdocs\test.html"
echo .success { color: green; } >> "C:\xampp\htdocs\test.html"
echo a { color: #0066cc; text-decoration: none; } >> "C:\xampp\htdocs\test.html"
echo a:hover { text-decoration: underline; } >> "C:\xampp\htdocs\test.html"
echo ^</style^> >> "C:\xampp\htdocs\test.html"
echo ^</head^> >> "C:\xampp\htdocs\test.html"
echo ^<body^> >> "C:\xampp\htdocs\test.html"
echo ^<h1^>🎉 Local Rust Website Test^</h1^> >> "C:\xampp\htdocs\test.html"
echo ^<p^>^<strong^>Status:^</strong^> ^<span class="success"^>Ready for testing!^</span^>^</p^> >> "C:\xampp\htdocs\test.html"
echo ^<h2^>Quick Links:^</h2^> >> "C:\xampp\htdocs\test.html"
echo ^<ul^> >> "C:\xampp\htdocs\test.html"
echo ^<li^>🏠 ^<a href="index.php"^>Main Website^</a^>^</li^> >> "C:\xampp\htdocs\test.html"
echo ^<li^>⚙️ ^<a href="compatibility.php"^>Compatibility Check^</a^>^</li^> >> "C:\xampp\htdocs\test.html"
echo ^<li^>👤 ^<a href="admin.php"^>Admin Panel^</a^>^</li^> >> "C:\xampp\htdocs\test.html"
echo ^<li^>🔗 ^<a href="link.php"^>Account Linking^</a^>^</li^> >> "C:\xampp\htdocs\test.html"
echo ^<li^>🗄️ ^<a href="phpmyadmin/"^>Database (phpMyAdmin)^</a^>^</li^> >> "C:\xampp\htdocs\test.html"
echo ^</ul^> >> "C:\xampp\htdocs\test.html"
echo ^</body^> >> "C:\xampp\htdocs\test.html"
echo ^</html^> >> "C:\xampp\htdocs\test.html"
echo ✅ Test page created

echo.
echo Starting XAMPP Control Panel...
start "" "C:\xampp\xampp-control.exe"

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo Next Steps:
echo 1. In XAMPP Control Panel:
echo    - Click "Start" next to Apache
echo    - Click "Start" next to MySQL
echo.
echo 2. Visit: http://localhost/test.html
echo.
echo 3. Configure your settings:
echo    - Steam API: C:\xampp\htdocs\steamauth\SteamConfig.php
echo    - Admin ID: C:\xampp\htdocs\admin\config.php
echo.
echo 4. Test your website at: http://localhost/
echo.
echo ========================================
echo.
pause
