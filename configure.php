<?php
/**
 * Quick Configuration Script for Rust Website + OutpostLink
 * Run this script to quickly configure your installation
 */

echo "=== Rust Website + OutpostLink Configuration Tool ===\n\n";

// Configuration values - EDIT THESE
$config = [
    'steam_api_key' => 'YOUR_STEAM_API_KEY_HERE',
    'admin_steam_id' => 'YOUR_STEAM_ID_HERE',
    'discord_client_id' => 'YOUR_DISCORD_CLIENT_ID',
    'discord_client_secret' => 'YOUR_DISCORD_CLIENT_SECRET',
    'discord_bot_token' => 'YOUR_DISCORD_BOT_TOKEN',
    'discord_guild_id' => 'YOUR_DISCORD_SERVER_ID',
    'web_secret' => 'YOUR_SECURE_WEB_SECRET',
    'domain' => 'https://your-domain.com',
    'db_host' => 'localhost',
    'db_username' => 'YOUR_DB_USERNAME',
    'db_password' => 'YOUR_DB_PASSWORD',
    'db_name' => 'YOUR_DB_NAME',
];

echo "Current configuration:\n";
foreach ($config as $key => $value) {
    echo "- $key: $value\n";
}

echo "\n=== INSTRUCTIONS ===\n";
echo "1. Edit this file (configure.php) and replace all 'YOUR_*' values with your actual credentials\n";
echo "2. Run this script again: php configure.php\n";
echo "3. Follow the setup guide in setup_guide.md\n\n";

// Check if configuration is still using placeholder values
$placeholders = array_filter($config, function($value) {
    return strpos($value, 'YOUR_') === 0;
});

if (!empty($placeholders)) {
    echo "❌ Please edit this file and replace the placeholder values:\n";
    foreach ($placeholders as $key => $value) {
        echo "   - $key: $value\n";
    }
    echo "\nAfter editing, run: php configure.php\n";
    exit(1);
}

echo "✅ Configuration looks good! Applying settings...\n\n";

// Apply Steam Auth configuration
$steamConfigPath = 'outpost-website-template-v1.1.1/steamauth/SteamConfig.php';
if (file_exists($steamConfigPath)) {
    $steamConfig = file_get_contents($steamConfigPath);
    $steamConfig = str_replace('YOUR_STEAM_API_KEY_HERE', $config['steam_api_key'], $steamConfig);
    file_put_contents($steamConfigPath, $steamConfig);
    echo "✅ Updated Steam authentication configuration\n";
}

// Apply Admin configuration
$adminConfigPath = 'outpost-website-template-v1.1.1/admin/config.php';
if (file_exists($adminConfigPath)) {
    $adminConfig = file_get_contents($adminConfigPath);
    $adminConfig = str_replace('YOUR_STEAM_ID_HERE', $config['admin_steam_id'], $adminConfig);
    file_put_contents($adminConfigPath, $adminConfig);
    echo "✅ Updated admin configuration\n";
}

// Apply OutpostLink configuration
$linkConfigPath = 'outpost-template-outpostlink-v1.1.2/web/link/config.php';
if (file_exists($linkConfigPath)) {
    $linkConfig = file_get_contents($linkConfigPath);
    $linkConfig = str_replace('YOUR_DB_USERNAME', $config['db_username'], $linkConfig);
    $linkConfig = str_replace('YOUR_DB_PASSWORD', $config['db_password'], $linkConfig);
    $linkConfig = str_replace('YOUR_DB_NAME', $config['db_name'], $linkConfig);
    $linkConfig = str_replace('YOUR_WEB_SECRET_HERE', $config['web_secret'], $linkConfig);
    $linkConfig = str_replace('YOUR_STEAM_API_KEY_HERE', $config['steam_api_key'], $linkConfig);
    $linkConfig = str_replace('YOUR_DISCORD_CLIENT_ID', $config['discord_client_id'], $linkConfig);
    $linkConfig = str_replace('YOUR_DISCORD_CLIENT_SECRET', $config['discord_client_secret'], $linkConfig);
    $linkConfig = str_replace('YOUR_DISCORD_BOT_TOKEN', $config['discord_bot_token'], $linkConfig);
    $linkConfig = str_replace('https://your-domain.com/link.php', $config['domain'] . '/link.php', $linkConfig);
    $linkConfig = str_replace('YOUR_DISCORD_SERVER_ID', $config['discord_guild_id'], $linkConfig);
    file_put_contents($linkConfigPath, $linkConfig);
    echo "✅ Updated OutpostLink configuration\n";
}

// Apply Bot configuration
$botConfigPath = 'outpost-template-outpostlink-v1.1.2/bot/botCon.json';
if (file_exists($botConfigPath)) {
    $botConfig = [
        'webSecret' => $config['web_secret'],
        'SteamAPIKey' => $config['steam_api_key'],
        'clientId' => $config['discord_client_id'],
        'token' => $config['discord_bot_token'],
        'apiURL' => $config['domain'] . '/link/api.php',
        'guildId' => $config['discord_guild_id']
    ];
    file_put_contents($botConfigPath, json_encode($botConfig, JSON_PRETTY_PRINT));
    echo "✅ Updated Discord bot configuration\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "1. Set up your web server (XAMPP/WAMP) and database\n";
echo "2. Copy website files to your web root\n";
echo "3. Create the database and table (see setup_guide.md)\n";
echo "4. Install Node.js dependencies for the bot: cd bot && npm install\n";
echo "5. Test your website at http://localhost/\n";
echo "6. Configure Discord roles and invite the bot to your server\n";
echo "7. Start the Discord bot: node bot/index.js\n";
echo "8. Upload the Rust plugin to your game server\n\n";
echo "📖 See setup_guide.md for detailed instructions!\n";
?>
