-- OutpostLink Database Setup
-- Run this SQL script to create the required database and table

-- Create database (modify name as needed)
CREATE DATABASE IF NOT EXISTS outpostlink;
USE outpostlink;

-- Create the main accounts table
CREATE TABLE IF NOT EXISTS outpostlink_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    steam_id VARCHAR(20) NOT NULL UNIQUE,
    discord_id VARCHAR(20) NOT NULL UNIQUE,
    username VA<PERSON><PERSON><PERSON>(100),
    discord_username VA<PERSON><PERSON><PERSON>(100),
    linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    vip_status BOOLEAN DEFAULT FALSE,
    booster_status BOOLEAN DEFAULT FALSE,
    steamgroup_status BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    INDEX idx_steam_id (steam_id),
    INDEX idx_discord_id (discord_id),
    INDEX idx_active (active)
);

-- Create a table for tracking role assignments
CREATE TABLE IF NOT EXISTS outpostlink_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_id INT NOT NULL,
    role_type VARCHAR(50) NOT NULL,
    role_id VARCHAR(20),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    removed_at TIMESTAMP NULL,
    active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (account_id) REFERENCES outpostlink_accounts(id) ON DELETE CASCADE,
    INDEX idx_account_role (account_id, role_type),
    INDEX idx_active_roles (active)
);

-- Create a table for API logs (optional, for debugging)
CREATE TABLE IF NOT EXISTS outpostlink_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action VARCHAR(100) NOT NULL,
    steam_id VARCHAR(20),
    discord_id VARCHAR(20),
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_action (action),
    INDEX idx_steam_id (steam_id),
    INDEX idx_created_at (created_at)
);

-- Insert some example data (remove in production)
-- INSERT INTO outpostlink_accounts (steam_id, discord_id, username, discord_username, vip_status) 
-- VALUES ('*****************', '123456789012345678', 'TestUser', 'TestUser#1234', FALSE);

-- Show created tables
SHOW TABLES;

-- Show table structure
DESCRIBE outpostlink_accounts;

SELECT 'Database setup completed successfully!' as Status;
