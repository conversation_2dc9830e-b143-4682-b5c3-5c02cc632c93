{"$schema": "https://json.schemastore.org/package.json", "name": "@discordjs/formatters", "version": "0.3.3", "description": "A set of functions to format strings for Discord.", "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}}, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["dist"], "contributors": ["Crawl <<EMAIL>>", "SpaceEEC <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Rom<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "keywords": [], "repository": {"type": "git", "url": "https://github.com/discordjs/discord.js.git", "directory": "packages/formatters"}, "bugs": {"url": "https://github.com/discordjs/discord.js/issues"}, "homepage": "https://discord.js.org", "dependencies": {"discord-api-types": "0.37.61"}, "devDependencies": {"@favware/cliff-jumper": "^2.2.1", "@types/node": "16.18.60", "@vitest/coverage-v8": "^0.34.6", "cross-env": "^7.0.3", "eslint": "^8.53.0", "eslint-config-neon": "^0.1.57", "eslint-formatter-pretty": "^5.0.0", "prettier": "^3.0.3", "tsup": "^7.2.0", "turbo": "^1.10.17-canary.0", "typescript": "^5.2.2", "vitest": "^0.34.6", "@discordjs/api-extractor": "^7.38.1"}, "engines": {"node": ">=16.11.0"}, "publishConfig": {"access": "public"}, "scripts": {"test": "vitest run", "build": "tsc --noEmit && tsup", "build:docs": "tsc -p tsconfig.docs.json", "lint": "prettier --check . && cross-env TIMING=1 eslint --format=pretty src __tests__", "format": "prettier --write . && cross-env TIMING=1 eslint --fix --format=pretty src __tests__", "docs": "pnpm run build:docs && api-extractor run --local", "changelog": "git cliff --prepend ./CHANGELOG.md -u -c ./cliff.toml -r ../../ --include-path 'packages/formatters/*'", "release": "cliff-jumper"}}