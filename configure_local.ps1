# Local Configuration Script for Rust Website
param(
    [string]$SteamApiKey = "",
    [string]$AdminSteamId = "",
    [string]$DiscordClientId = "",
    [string]$DiscordBotToken = "",
    [string]$WebSecret = ""
)

Write-Host "=== Local Rust Website Configuration ===" -ForegroundColor Green
Write-Host ""

# Check if XAMPP is installed
$xamppPath = "C:\xampp"
$htdocsPath = "$xamppPath\htdocs"

if (-not (Test-Path $xamppPath)) {
    Write-Host "❌ XAMPP not found at $xamppPath" -ForegroundColor Red
    Write-Host "Please install XAMPP first from https://www.apachefriends.org/" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ XAMPP found at $xamppPath" -ForegroundColor Green

# Check if website files are copied
if (-not (Test-Path "$htdocsPath\index.php")) {
    Write-Host "❌ Website files not found in $htdocsPath" -ForegroundColor Red
    Write-Host "Copying website files..." -ForegroundColor Yellow
    
    try {
        Copy-Item -Path "outpost-website-template-v1.1.1\*" -Destination $htdocsPath -Recurse -Force
        Copy-Item -Path "outpost-template-outpostlink-v1.1.2\web\*" -Destination $htdocsPath -Recurse -Force
        Write-Host "✅ Website files copied successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to copy website files: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Website files found in $htdocsPath" -ForegroundColor Green
}

# Interactive configuration if parameters not provided
if (-not $SteamApiKey) {
    Write-Host ""
    Write-Host "🔑 Steam API Key Configuration" -ForegroundColor Cyan
    Write-Host "Get your Steam API key from: https://steamcommunity.com/dev/apikey" -ForegroundColor Yellow
    $SteamApiKey = Read-Host "Enter your Steam API Key (or press Enter to skip)"
}

if (-not $AdminSteamId) {
    Write-Host ""
    Write-Host "👤 Admin Steam ID Configuration" -ForegroundColor Cyan
    Write-Host "Find your Steam ID at: https://steamid.io/" -ForegroundColor Yellow
    $AdminSteamId = Read-Host "Enter your Steam ID (or press Enter to skip)"
}

# Configure Steam Authentication
if ($SteamApiKey) {
    $steamConfigPath = "$htdocsPath\steamauth\SteamConfig.php"
    if (Test-Path $steamConfigPath) {
        $steamConfig = Get-Content $steamConfigPath -Raw
        $steamConfig = $steamConfig -replace 'YOUR_STEAM_API_KEY_HERE', $SteamApiKey
        $steamConfig | Set-Content $steamConfigPath
        Write-Host "✅ Steam API key configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Steam config file not found at $steamConfigPath" -ForegroundColor Yellow
    }
}

# Configure Admin Access
if ($AdminSteamId) {
    $adminConfigPath = "$htdocsPath\admin\config.php"
    if (Test-Path $adminConfigPath) {
        $adminConfig = Get-Content $adminConfigPath -Raw
        $adminConfig = $adminConfig -replace 'YOUR_STEAM_ID_HERE', $AdminSteamId
        $adminConfig | Set-Content $adminConfigPath
        Write-Host "✅ Admin Steam ID configured" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Admin config file not found at $adminConfigPath" -ForegroundColor Yellow
    }
}

# Create a simple test page
$testPageContent = @'
<?php
echo "<h1>🎉 Local Rust Website Test</h1>";
echo "<p><strong>PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<hr>";
echo "<h2>🔗 Quick Links:</h2>";
echo "<ul>";
echo "<li><a href='index.php'>🏠 Main Website</a></li>";
echo "<li><a href='compatibility.php'>⚙️ Compatibility Check</a></li>";
echo "<li><a href='admin.php'>👤 Admin Panel</a></li>";
echo "<li><a href='link.php'>🔗 Account Linking</a></li>";
echo "<li><a href='phpmyadmin/'>🗄️ Database (phpMyAdmin)</a></li>";
echo "</ul>";
echo "<hr>";
echo "<h2>📋 Configuration Status:</h2>";
echo "<ul>";
echo "<li>ℹ️ Check configuration files manually</li>";
echo "</ul>";
?>
'@

$testPageContent | Set-Content "$htdocsPath\test.php"
Write-Host "✅ Test page created at /test.php" -ForegroundColor Green

Write-Host ""
Write-Host "=== Configuration Complete! ===" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Start XAMPP Control Panel (run as administrator)" -ForegroundColor White
Write-Host "2. Start Apache and MySQL services" -ForegroundColor White
Write-Host "3. Visit http://localhost/test.php to verify setup" -ForegroundColor White
Write-Host ""
Write-Host "🔗 Important URLs:" -ForegroundColor Cyan
Write-Host "   Test Page: http://localhost/test.php" -ForegroundColor White
Write-Host "   Main Site: http://localhost/" -ForegroundColor White
Write-Host "   Admin Panel: http://localhost/admin.php" -ForegroundColor White
Write-Host "   Database: http://localhost/phpmyadmin/" -ForegroundColor White
Write-Host ""

# Offer to start XAMPP
$startXampp = Read-Host "Would you like to start XAMPP Control Panel now? (y/n)"
if ($startXampp -eq 'y' -or $startXampp -eq 'Y') {
    Start-Process "$xamppPath\xampp-control.exe" -Verb RunAs
    Write-Host "✅ XAMPP Control Panel started" -ForegroundColor Green
    Write-Host "Remember to start Apache and MySQL services!" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 Setup complete! Your local Rust website is ready for testing." -ForegroundColor Green
