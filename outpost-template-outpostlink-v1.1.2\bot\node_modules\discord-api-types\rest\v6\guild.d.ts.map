{"version": 3, "file": "guild.d.ts", "sourceRoot": "", "sources": ["guild.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACX,MAAM,EACN,UAAU,EACV,QAAQ,EACR,mBAAmB,EACnB,cAAc,EACd,eAAe,EACf,sBAAsB,EACtB,SAAS,EACT,OAAO,EACP,cAAc,EACd,gCAAgC,EAChC,0BAA0B,EAC1B,YAAY,EACZ,sBAAsB,EACtB,gBAAgB,EAChB,yBAAyB,EACzB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,oCAAoC,EAAE,MAAM,WAAW,CAAC;AAEtE;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,oCAAoC;IACpF,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;CACpB;AAED;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,OAAO,CACjD,IAAI,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,SAAS,GAAG,YAAY,GAAG,qBAAqB,CAAC,CAC9F,GAAG;IACH,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACjC,SAAS,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IACxC,qBAAqB,CAAC,EAAE,uBAAuB,EAAE,GAAG,SAAS,CAAC;CAC9D,CAAC;AAEF;;GAEG;AACH,MAAM,WAAW,kBAAmB,SAAQ,4BAA4B;IACvE,EAAE,EAAE,MAAM,GAAG,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,yBAAyB;IACzC,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC5B,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,kBAAkB,CAAC,EAAE,sBAAsB,GAAG,SAAS,CAAC;IACxD,6BAA6B,CAAC,EAAE,gCAAgC,GAAG,SAAS,CAAC;IAC7E,uBAAuB,CAAC,EAAE,0BAA0B,GAAG,SAAS,CAAC;IACjE,KAAK,CAAC,EAAE,kBAAkB,EAAE,GAAG,SAAS,CAAC;IACzC,QAAQ,CAAC,EAAE,4BAA4B,EAAE,GAAG,SAAS,CAAC;IACtD,cAAc,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IAC7C,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC,iBAAiB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;CAChD;AAED;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,QAAQ,CAAC;AAE/C;;;;GAIG;AACH,MAAM,WAAW,oBAAoB;IACpC,WAAW,CAAC,EAAE,OAAO,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,qBAAqB,GAAG,QAAQ,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,eAAe,CAAC;AAE3D;;;;GAIG;AACH,MAAM,WAAW,yBAAyB;IACzC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC5B,kBAAkB,CAAC,EAAE,sBAAsB,GAAG,SAAS,CAAC;IACxD,6BAA6B,CAAC,EAAE,gCAAgC,GAAG,SAAS,CAAC;IAC7E,uBAAuB,CAAC,EAAE,0BAA0B,GAAG,SAAS,CAAC;IACjE,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC3C,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACjC,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9B,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7C,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC9C,gBAAgB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAC7C,yBAAyB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACtD,gBAAgB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACtC,QAAQ,CAAC,EAAE,YAAY,EAAE,GAAG,SAAS,CAAC;IACtC,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACxC;AAED;;GAEG;AACH,MAAM,MAAM,uBAAuB,GAAG,QAAQ,CAAC;AAE/C;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,6BAA6B,GAAG,UAAU,EAAE,CAAC;AAEzD;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,OAAO,CACpD,IAAI,CACH,UAAU,EACV,MAAM,GAAG,uBAAuB,GAAG,OAAO,GAAG,MAAM,GAAG,SAAS,GAAG,YAAY,GAAG,qBAAqB,GAAG,WAAW,CACpH,CACD,GACA,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;AAEpC;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG,UAAU,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,yCAAyC,GAAG;IACvD,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,EAAE,MAAM,CAAC;IACjB,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACvC,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACtC,EAAE,CAAC;AAEJ;;GAEG;AACH,MAAM,MAAM,uCAAuC,GAAG,KAAK,CAAC;AAE5D;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,cAAc,CAAC;AAEzD;;;;GAIG;AACH,MAAM,WAAW,2BAA2B;IAC3C,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG,cAAc,EAAE,CAAC;AAE5D;;GAEG;AACH,MAAM,WAAW,iCAAiC;IACjD,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,cAAc,EAAE,CAAC;AAElE;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC7C,YAAY,EAAE,MAAM,CAAC;IACrB,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;IAC7B,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC3B,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,MAAM,2BAA2B,GAAG,cAAc,GAAG,SAAS,CAAC;AAErE;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,KAAK,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACvC;AAED;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG,KAAK,CAAC;AAElD;;;;GAIG;AACH,MAAM,WAAW,8CAA8C;IAC9D,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACjC;AAED;;GAEG;AACH,MAAM,MAAM,4CAA4C,GAAG,QAAQ,CAAC,8CAA8C,CAAC,CAAC;AAEpH;;;;GAIG;AACH,MAAM,MAAM,+BAA+B,GAAG,KAAK,CAAC;AAEpD;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,8BAA8B,GAAG,KAAK,CAAC;AAEnD;;;;GAIG;AACH,MAAM,MAAM,yBAAyB,GAAG,MAAM,EAAE,CAAC;AAEjD;;;;GAIG;AACH,MAAM,MAAM,wBAAwB,GAAG,MAAM,CAAC;AAE9C;;;;GAIG;AACH,MAAM,WAAW,0BAA0B;IAC1C,mBAAmB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACzC,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC5B;AAED;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,2BAA2B,GAAG,KAAK,CAAC;AAEhD;;;;GAIG;AACH,MAAM,MAAM,0BAA0B,GAAG,OAAO,EAAE,CAAC;AAEnD;;;;GAIG;AACH,MAAM,WAAW,4BAA4B;IAC5C,IAAI,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjC,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACjD,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAClC,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;IACnC,WAAW,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CACzC;AAED;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG,OAAO,CAAC;AAEjD;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG;IACpD,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CAC9B,EAAE,CAAC;AAEJ;;GAEG;AACH,MAAM,MAAM,oCAAoC,GAAG,OAAO,EAAE,CAAC;AAE7D;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC7C,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IAC1C,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC3B,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC5B,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CAClC;AAED;;GAEG;AACH,MAAM,MAAM,2BAA2B,GAAG,OAAO,CAAC;AAElD;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,KAAK,CAAC;AAEjD;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;;OAKG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;CACvB;AAED;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC/C,MAAM,EAAE,MAAM,CAAC;CACf;AAED;;;;GAIG;AACH,MAAM,WAAW,6BAA6B;IAC7C,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,mBAAmB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC1C,aAAa,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;CACrC;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC3C,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;CACtB;AAED;;;;GAIG;AACH,MAAM,MAAM,iCAAiC,GAAG,cAAc,EAAE,CAAC;AAEjE;;;;GAIG;AACH,MAAM,MAAM,4BAA4B,GAAG,SAAS,EAAE,CAAC;AAEvD;;;;GAIG;AACH,MAAM,WAAW,gCAAgC;IAChD,oBAAoB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,mBAAmB,EAAE,CAAC;AAEtE;;;;GAIG;AACH,MAAM,WAAW,mCAAmC;IACnD,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,EAAE,MAAM,CAAC;CACX;AAED;;GAEG;AACH,MAAM,MAAM,iCAAiC,GAAG,KAAK,CAAC;AAEtD;;;;GAIG;AACH,MAAM,WAAW,oCAAoC;IACpD,eAAe,CAAC,EAAE,yBAAyB,GAAG,IAAI,GAAG,SAAS,CAAC;IAC/D,mBAAmB,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IAChD,gBAAgB,CAAC,EAAE,OAAO,GAAG,IAAI,GAAG,SAAS,CAAC;CAC9C;AAED;;GAEG;AACH,MAAM,MAAM,kCAAkC,GAAG,KAAK,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,KAAK,CAAC;AAExD;;;;GAIG;AACH,MAAM,MAAM,qCAAqC,GAAG,KAAK,CAAC;AAE1D;;;GAGG;AACH,MAAM,MAAM,2BAA2B,GAAG,sBAAsB,CAAC;AAEjE;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,sBAAsB,CAAC;AAEzE;;;GAGG;AACH,MAAM,MAAM,+BAA+B,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAE9E;;;;GAIG;AACH,MAAM,MAAM,uCAAuC,GAAG,OAAO,CAAC,sBAAsB,CAAC,CAAC;AAEtF;;;GAGG;AACH,MAAM,MAAM,6BAA6B,GAAG,sBAAsB,CAAC;AAEnE;;GAEG;AACH,MAAM,MAAM,qCAAqC,GAAG,sBAAsB,CAAC;AAE3E;;;;GAIG;AACH,MAAM,WAAW,8BAA8B;IAC9C,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;CACb;AAED;;;;GAIG;AACH,MAAM,WAAW,+BAA+B;IAC/C,KAAK,CAAC,EAAE,gBAAgB,CAAC;CACzB;AAED;;;;;GAKG;AACH,MAAM,MAAM,gCAAgC,GAAG,WAAW,CAAC"}