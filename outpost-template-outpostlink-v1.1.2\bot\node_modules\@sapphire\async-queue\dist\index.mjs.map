{"version": 3, "sources": ["../src/lib/AsyncQueueEntry.ts", "../src/lib/AsyncQueue.ts"], "sourcesContent": ["import type { AsyncQueue } from './AsyncQueue';\n\n/**\n * @internal\n */\nexport class AsyncQueueEntry {\n\tpublic readonly promise: Promise<void>;\n\tprivate resolve!: () => void;\n\tprivate reject!: (error: Error) => void;\n\tprivate readonly queue: AsyncQueue;\n\tprivate signal: PolyFillAbortSignal | null = null;\n\tprivate signalListener: (() => void) | null = null;\n\n\tpublic constructor(queue: AsyncQueue) {\n\t\tthis.queue = queue;\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n\n\tpublic setSignal(signal: AbortSignal) {\n\t\tif (signal.aborted) return this;\n\n\t\tthis.signal = signal as PolyFillAbortSignal;\n\t\tthis.signalListener = () => {\n\t\t\tconst index = this.queue['promises'].indexOf(this);\n\t\t\tif (index !== -1) this.queue['promises'].splice(index, 1);\n\n\t\t\tthis.reject(new Error('Request aborted manually'));\n\t\t};\n\t\tthis.signal.addEventListener('abort', this.signalListener);\n\t\treturn this;\n\t}\n\n\tpublic use() {\n\t\tthis.dispose();\n\t\tthis.resolve();\n\t\treturn this;\n\t}\n\n\tpublic abort() {\n\t\tthis.dispose();\n\t\tthis.reject(new Error('Request aborted manually'));\n\t\treturn this;\n\t}\n\n\tprivate dispose() {\n\t\tif (this.signal) {\n\t\t\tthis.signal.removeEventListener('abort', this.signalListener!);\n\t\t\tthis.signal = null;\n\t\t\tthis.signalListener = null;\n\t\t}\n\t}\n}\n\ninterface PolyFillAbortSignal {\n\treadonly aborted: boolean;\n\taddEventListener(type: 'abort', listener: () => void): void;\n\tremoveEventListener(type: 'abort', listener: () => void): void;\n}\n", "import { AsyncQueueEntry } from './AsyncQueueEntry';\n\n/**\n * The AsyncQueue class used to sequentialize burst requests\n */\nexport class AsyncQueue {\n\t/**\n\t * The amount of entries in the queue, including the head.\n\t * @seealso {@link queued} for the queued count.\n\t */\n\tpublic get remaining(): number {\n\t\treturn this.promises.length;\n\t}\n\n\t/**\n\t * The amount of queued entries.\n\t * @seealso {@link remaining} for the count with the head.\n\t */\n\tpublic get queued(): number {\n\t\treturn this.remaining === 0 ? 0 : this.remaining - 1;\n\t}\n\n\t/**\n\t * The promises array\n\t */\n\tprivate promises: AsyncQueueEntry[] = [];\n\n\t/**\n\t * Waits for last promise and queues a new one\n\t * @example\n\t * ```typescript\n\t * const queue = new AsyncQueue();\n\t * async function request(url, options) {\n\t *     await queue.wait({ signal: options.signal });\n\t *     try {\n\t *         const result = await fetch(url, options);\n\t *         // Do some operations with 'result'\n\t *     } finally {\n\t *         // Remove first entry from the queue and resolve for the next entry\n\t *         queue.shift();\n\t *     }\n\t * }\n\t *\n\t * request(someUrl1, someOptions1); // Will call fetch() immediately\n\t * request(someUrl2, someOptions2); // Will call fetch() after the first finished\n\t * request(someUrl3, someOptions3); // Will call fetch() after the second finished\n\t * ```\n\t */\n\tpublic wait(options?: Readonly<AsyncQueueWaitOptions>): Promise<void> {\n\t\tconst entry = new AsyncQueueEntry(this);\n\n\t\tif (this.promises.length === 0) {\n\t\t\tthis.promises.push(entry);\n\t\t\treturn Promise.resolve();\n\t\t}\n\n\t\tthis.promises.push(entry);\n\t\tif (options?.signal) entry.setSignal(options.signal);\n\t\treturn entry.promise;\n\t}\n\n\t/**\n\t * Unlocks the head lock and transfers the next lock (if any) to the head.\n\t */\n\tpublic shift(): void {\n\t\tif (this.promises.length === 0) return;\n\t\tif (this.promises.length === 1) {\n\t\t\t// Remove the head entry.\n\t\t\tthis.promises.shift();\n\t\t\treturn;\n\t\t}\n\n\t\t// Remove the head entry, making the 2nd entry the new one.\n\t\t// Then use the head entry, which will unlock the promise.\n\t\tthis.promises.shift();\n\t\tthis.promises[0].use();\n\t}\n\n\t/**\n\t * Aborts all the pending promises.\n\t * @note To avoid race conditions, this does **not** unlock the head lock.\n\t */\n\tpublic abortAll(): void {\n\t\t// If there are no queued entries, skip early.\n\t\tif (this.queued === 0) return;\n\n\t\t// Abort all the entries except the head, that is why the loop starts at\n\t\t// 1 and not at 0.\n\t\tfor (let i = 1; i < this.promises.length; ++i) {\n\t\t\tthis.promises[i].abort();\n\t\t}\n\n\t\tthis.promises.length = 1;\n\t}\n}\n\nexport interface AsyncQueueWaitOptions {\n\tsignal?: AbortSignal | undefined | null;\n}\n"], "mappings": ";;;;;;;;;AAKO,IAAM,kBAAN,MAAsB;AAAA,EAQrB,YAAY,OAAmB;AAPtC,wBAAgB;AAChB,wBAAQ;AACR,wBAAQ;AACR,wBAAiB;AACjB,wBAAQ,UAAqC;AAC7C,wBAAQ,kBAAsC;AAG7C,SAAK,QAAQ;AACb,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IACf,CAAC;AAAA,EACF;AAAA,EAEO,UAAU,QAAqB;AACrC,QAAI,OAAO;AAAS,aAAO;AAE3B,SAAK,SAAS;AACd,SAAK,iBAAiB,MAAM;AAC3B,YAAM,QAAQ,KAAK,MAAM,YAAY,QAAQ,IAAI;AACjD,UAAI,UAAU;AAAI,aAAK,MAAM,YAAY,OAAO,OAAO,CAAC;AAExD,WAAK,OAAO,IAAI,MAAM,0BAA0B,CAAC;AAAA,IAClD;AACA,SAAK,OAAO,iBAAiB,SAAS,KAAK,cAAc;AACzD,WAAO;AAAA,EACR;AAAA,EAEO,MAAM;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,WAAO;AAAA,EACR;AAAA,EAEO,QAAQ;AACd,SAAK,QAAQ;AACb,SAAK,OAAO,IAAI,MAAM,0BAA0B,CAAC;AACjD,WAAO;AAAA,EACR;AAAA,EAEQ,UAAU;AACjB,QAAI,KAAK,QAAQ;AAChB,WAAK,OAAO,oBAAoB,SAAS,KAAK,cAAe;AAC7D,WAAK,SAAS;AACd,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACD;AACD;AAjDa;;;ACAN,IAAM,aAAN,MAAiB;AAAA,EAAjB;AAoBN,wBAAQ,YAA8B,CAAC;AAAA;AAAA,EAfvC,IAAW,YAAoB;AAC9B,WAAO,KAAK,SAAS;AAAA,EACtB;AAAA,EAMA,IAAW,SAAiB;AAC3B,WAAO,KAAK,cAAc,IAAI,IAAI,KAAK,YAAY;AAAA,EACpD;AAAA,EA4BO,KAAK,SAA0D;AACrE,UAAM,QAAQ,IAAI,gBAAgB,IAAI;AAEtC,QAAI,KAAK,SAAS,WAAW,GAAG;AAC/B,WAAK,SAAS,KAAK,KAAK;AACxB,aAAO,QAAQ,QAAQ;AAAA,IACxB;AAEA,SAAK,SAAS,KAAK,KAAK;AACxB,QAAI,SAAS;AAAQ,YAAM,UAAU,QAAQ,MAAM;AACnD,WAAO,MAAM;AAAA,EACd;AAAA,EAKO,QAAc;AACpB,QAAI,KAAK,SAAS,WAAW;AAAG;AAChC,QAAI,KAAK,SAAS,WAAW,GAAG;AAE/B,WAAK,SAAS,MAAM;AACpB;AAAA,IACD;AAIA,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,GAAG,IAAI;AAAA,EACtB;AAAA,EAMO,WAAiB;AAEvB,QAAI,KAAK,WAAW;AAAG;AAIvB,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,EAAE,GAAG;AAC9C,WAAK,SAAS,GAAG,MAAM;AAAA,IACxB;AAEA,SAAK,SAAS,SAAS;AAAA,EACxB;AACD;AAzFa;", "names": []}