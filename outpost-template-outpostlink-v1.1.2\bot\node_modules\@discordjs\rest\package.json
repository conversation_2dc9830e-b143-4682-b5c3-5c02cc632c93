{"$schema": "https://json.schemastore.org/package.json", "name": "@discordjs/rest", "version": "2.2.0", "description": "The REST API for discord.js", "exports": {".": {"node": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "default": {"require": {"types": "./dist/web.d.ts", "default": "./dist/web.js"}, "import": {"types": "./dist/web.d.mts", "default": "./dist/web.mjs"}}}, "./*": {"require": {"types": "./dist/strategies/*.d.ts", "default": "./dist/strategies/*.js"}, "import": {"types": "./dist/strategies/*.d.mts", "default": "./dist/strategies/*.mjs"}}}, "types": "./dist/index.d.ts", "directories": {"lib": "src", "test": "__tests__"}, "files": ["dist"], "contributors": ["Crawl <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "SpaceEEC <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> Rom<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "keywords": ["discord", "api", "rest", "discordapp", "discordjs"], "repository": {"type": "git", "url": "https://github.com/discordjs/discord.js.git", "directory": "packages/rest"}, "bugs": {"url": "https://github.com/discordjs/discord.js/issues"}, "homepage": "https://discord.js.org", "dependencies": {"@sapphire/async-queue": "^1.5.0", "@sapphire/snowflake": "^3.5.1", "@vladfrangu/async_event_emitter": "^2.2.2", "discord-api-types": "0.37.61", "magic-bytes.js": "^1.5.0", "tslib": "^2.6.2", "undici": "5.27.2", "@discordjs/util": "^1.0.2", "@discordjs/collection": "^2.0.0"}, "devDependencies": {"@favware/cliff-jumper": "^2.2.1", "@types/node": "18.17.9", "@vitest/coverage-v8": "^0.34.6", "cross-env": "^7.0.3", "esbuild-plugin-version-injector": "^1.2.1", "eslint": "^8.53.0", "eslint-config-neon": "^0.1.57", "eslint-formatter-pretty": "^5.0.0", "prettier": "^3.1.0", "tsup": "^7.2.0", "turbo": "^1.10.17-canary.0", "typescript": "^5.2.2", "vitest": "^0.34.6", "@discordjs/api-extractor": "^7.38.1"}, "engines": {"node": ">=16.11.0"}, "publishConfig": {"access": "public"}, "scripts": {"test": "vitest run", "build": "tsc --noEmit && tsup", "build:docs": "tsc -p tsconfig.docs.json", "lint": "prettier --check . && cross-env TIMING=1 eslint --format=pretty src __tests__", "format": "prettier --write . && cross-env TIMING=1 eslint --fix --format=pretty src __tests__", "fmt": "pnpm run format", "docs": "pnpm run build:docs && api-extractor run --local --minify", "changelog": "git cliff --prepend ./CHANGELOG.md -u -c ./cliff.toml -r ../../ --include-path 'packages/rest/*'", "release": "cliff-jumper"}}