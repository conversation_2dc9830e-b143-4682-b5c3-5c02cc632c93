# Complete Setup Guide for Rust Website + OutpostLink

## Prerequisites

1. **Web Server**: XAMPP, WAMP, or hosting provider with PHP 8.1+
2. **Database**: MySQL/MariaDB
3. **Node.js**: Version 16+ for Discord bot
4. **Steam API Key**: Get from https://steamcommunity.com/dev/apikey
5. **Discord Application**: Create at https://discord.com/developers/applications

## Phase 1: Basic Website Setup

### 1. Install Web Environment
- Download and install XAMPP from https://www.apachefriends.org/
- Start Apache and MySQL services

### 2. Upload Website Files
- Copy all files from `outpost-website-template-v1.1.1/` to your web root (htdocs)
- Visit `http://localhost/compatibility.php` to check requirements

### 3. Configure Steam Authentication
Edit `steamauth/SteamConfig.php`:
```php
$steamauth['apikey'] = "YOUR_STEAM_API_KEY_HERE";
```

### 4. Configure Admin Access
Edit `admin/config.php`:
```php
'admins' => [
    'YOUR_STEAM_ID_HERE', // Get from steamid.io
],
```

### 5. Test Basic Website
- Visit `http://localhost/`
- Click Steam login button
- Access Web Panel from footer if admin configured correctly

## Phase 2: Discord Application Setup

### 1. Create Discord Application
1. Go to https://discord.com/developers/applications
2. Click "New Application"
3. Name it (e.g., "OutpostLink")
4. Note the **Client ID**

### 2. Create Bot
1. Go to "Bot" section
2. Click "Add Bot"
3. Copy the **Bot Token**
4. Enable "Server Members Intent"

### 3. Configure OAuth2
1. Go to "OAuth2" section
2. Copy **Client Secret**
3. Add redirect URL: `https://your-domain.com/link.php`
4. Generate OAuth2 URL with scopes: `identify`, `guilds`, `guilds.join`, `bot`, `applications.commands`
5. Bot permissions: `Manage Roles`, `Manage Nicknames`, `Send Messages`

## Phase 3: Database Setup

### 1. Create Database
```sql
CREATE DATABASE outpostlink;
USE outpostlink;

CREATE TABLE outpostlink_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    steam_id VARCHAR(20) NOT NULL UNIQUE,
    discord_id VARCHAR(20) NOT NULL UNIQUE,
    username VARCHAR(100),
    linked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    vip_status BOOLEAN DEFAULT FALSE,
    booster_status BOOLEAN DEFAULT FALSE,
    steamgroup_status BOOLEAN DEFAULT FALSE
);
```

## Phase 4: OutpostLink Configuration

### 1. Copy OutpostLink Files
- Copy all files from `outpost-template-outpostlink-v1.1.2/web/` to your website root

### 2. Configure OutpostLink
Edit `link/config.php` with your values:
- Database credentials
- Discord bot settings
- API secret (create a secure password)
- Role IDs from your Discord server

### 3. Run Installation
- Visit your website's Web Panel
- Go to "Account Linking"
- Complete the installation form

## Phase 5: Discord Bot Setup

### 1. Install Node.js Dependencies
```bash
cd outpost-template-outpostlink-v1.1.2/bot/
npm install
```

### 2. Configure Bot
Edit `botCon.json`:
```json
{
    "webSecret": "YOUR_WEB_SECRET",
    "SteamAPIKey": "YOUR_STEAM_API_KEY",
    "clientId": "YOUR_DISCORD_CLIENT_ID",
    "token": "YOUR_DISCORD_BOT_TOKEN",
    "apiURL": "https://your-domain.com/link/api.php",
    "guildId": "YOUR_DISCORD_SERVER_ID"
}
```

### 3. Start Bot
```bash
node index.js
```

## Phase 6: Rust Server Plugin

### 1. Upload Plugin
- Copy `rust/plugins/OutpostLink.cs` to your Rust server's plugins folder
- Copy `rust/config/OutpostLink.json` to your server's config folder

### 2. Configure Plugin
Edit `config/OutpostLink.json`:
```json
{
    "API_URL": "https://your-domain.com/link/api.php",
    "API_SECRET": "YOUR_WEB_SECRET",
    "Groups": {
        "linked": "linked",
        "vip": "vip",
        "booster": "booster"
    }
}
```

## Testing the Complete System

1. **Website**: Visit your site, test Steam login
2. **Admin Panel**: Access web panel, configure settings
3. **Account Linking**: Visit `/link.php`, test Steam + Discord linking
4. **Discord Bot**: Invite bot to server, test `/sync` command
5. **Rust Server**: Test `/link` command in-game

## Troubleshooting

- Check PHP error logs for website issues
- Check bot console for Discord connection issues
- Verify all API keys and tokens are correct
- Ensure database permissions are set correctly
- Check file permissions (644 for files, 755 for directories)

## Security Notes

- Use strong passwords for database and API secret
- Keep bot token secure and never share it
- Regularly update all components
- Use HTTPS in production
