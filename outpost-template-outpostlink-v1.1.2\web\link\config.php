<?php

/**
 * OutpostLink Configuration File
 * This file will be auto-generated by the installation process
 * Manual configuration is also possible by editing the values below
 */

return [

    /*
    |--------------------------------------------------------------------------
    | Link System Settings
    |--------------------------------------------------------------------------
    |
    | Basic configuration for the account linking system
    |
    */

    'link' => [
        'enabled' => 'yes',
        'heading' => 'Account Linking',
        'text' => 'Link your Steam and Discord accounts to access VIP features and roles on our servers.',
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Database connection settings for storing linked accounts
    |
    */

    'database' => [
        'host' => 'localhost',
        'username' => 'YOUR_DB_USERNAME',
        'password' => 'YOUR_DB_PASSWORD',
        'database' => 'YOUR_DB_NAME',
        'table' => 'outpostlink_accounts',
    ],

    /*
    |--------------------------------------------------------------------------
    | API Configuration
    |--------------------------------------------------------------------------
    |
    | Settings for the web API that communicates with the bot and game server
    |
    */

    'api' => [
        'secret' => 'YOUR_WEB_SECRET_HERE', // Secure password for API access
        'steam_api_key' => 'YOUR_STEAM_API_KEY_HERE',
    ],

    /*
    |--------------------------------------------------------------------------
    | Discord Bot Configuration
    |--------------------------------------------------------------------------
    |
    | Discord application and bot settings
    |
    */

    'discord_bot' => [
        'client_id' => 'YOUR_DISCORD_CLIENT_ID',
        'client_secret' => 'YOUR_DISCORD_CLIENT_SECRET',
        'bot_token' => 'YOUR_DISCORD_BOT_TOKEN',
        'redirect_uri' => 'https://your-domain.com/link.php', // Must match Discord OAuth2 settings
        'guild_id' => 'YOUR_DISCORD_SERVER_ID',
        
        // Role IDs for automatic assignment
        'linked_role_id' => 'YOUR_LINKED_ROLE_ID',
        'vip_role_id' => 'YOUR_VIP_ROLE_ID',
        'booster_role_id' => '', // Optional: Discord Booster role
        'steamgroup_role_id' => '', // Optional: Steam Group member role
        'steamgroup_id' => '', // Optional: Steam Group ID
    ],

    /*
    |--------------------------------------------------------------------------
    | Rust Server Groups
    |--------------------------------------------------------------------------
    |
    | Define the groups that will be synced with the Rust server
    |
    */

    'rust_groups' => [
        'linked' => 'linked',
        'vip' => 'vip',
        'booster' => 'booster',
        'steamgroup' => 'steamgroup',
    ],

];
