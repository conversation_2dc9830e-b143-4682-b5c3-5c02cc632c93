@echo off
echo ========================================
echo    Rust Website Local Server Starter
echo ========================================
echo.

echo Checking if XAMPP is installed...
if not exist "C:\xampp\xampp-control.exe" (
    echo ❌ XAMPP not found at C:\xampp\
    echo.
    echo Please install XAMPP first:
    echo 1. Go to https://www.apachefriends.org/
    echo 2. Download XAMPP for Windows
    echo 3. Install to C:\xampp\
    echo 4. Run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ XAMPP found!
echo.

echo Starting XAMPP Control Panel...
echo (You'll need to manually start Apache and MySQL)
echo.
start "" "C:\xampp\xampp-control.exe"

echo Waiting 5 seconds for XAMPP to load...
timeout /t 5 /nobreak > nul

echo.
echo ========================================
echo    Next Steps:
echo ========================================
echo.
echo 1. In XAMPP Control Panel:
echo    - Click "Start" next to Apache
echo    - Click "Start" next to MySQL
echo.
echo 2. Once both are running, visit:
echo    🌐 http://localhost/
echo.
echo 3. Available pages:
echo    🏠 Main Site: http://localhost/
echo    ⚙️  Compatibility: http://localhost/compatibility.php
echo    👤 Admin Panel: http://localhost/admin.php
echo    🔗 Account Linking: http://localhost/link.php
echo.
echo 4. Database Management:
echo    🗄️  phpMyAdmin: http://localhost/phpmyadmin/
echo.
echo ========================================
echo    Configuration Files:
echo ========================================
echo.
echo Steam API Key: C:\xampp\htdocs\steamauth\SteamConfig.php
echo Admin Steam ID: C:\xampp\htdocs\admin\config.php
echo OutpostLink Config: C:\xampp\htdocs\link\config.php
echo.
echo ========================================
echo.
echo Press any key to open your website...
pause > nul

echo Opening website in browser...
start "" "http://localhost/"

echo.
echo ✅ Local server setup complete!
echo.
echo If you see any errors:
echo 1. Check that Apache and MySQL are running in XAMPP
echo 2. Review the MANUAL_SETUP_GUIDE.md file
echo 3. Check C:\xampp\apache\logs\error.log for details
echo.
pause
