"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
var __asyncDelegator = (this && this.__asyncDelegator) || function (o) {
    var i, p;
    return i = {}, verb("next"), verb("throw", function (e) { throw e; }), verb("return"), i[Symbol.iterator] = function () { return this; }, i;
    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }
};
var __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i;
    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }
    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }
    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }
    function fulfill(value) { resume("next", value); }
    function reject(value) { resume("throw", value); }
    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
const events_1 = __importDefault(require("events"));
const mysql2_1 = __importDefault(require("mysql2"));
const pool_1 = require("./pool");
const keyvMysqlKeys = new Set(['adapter', 'compression', 'connect', 'dialect', 'keySize', 'table', 'ttl', 'uri']);
class KeyvMysql extends events_1.default {
    constructor(keyvOptions) {
        super();
        this.ttlSupport = false;
        let options = {
            dialect: 'mysql',
            uri: 'mysql://localhost',
        };
        if (typeof keyvOptions === 'string') {
            options.uri = keyvOptions;
        }
        else {
            options = Object.assign(Object.assign({}, options), keyvOptions);
        }
        const mysqlOptions = Object.fromEntries(Object.entries(options).filter(([k]) => !keyvMysqlKeys.has(k)));
        delete mysqlOptions.namespace;
        delete mysqlOptions.serialize;
        delete mysqlOptions.deserialize;
        const connection = () => __awaiter(this, void 0, void 0, function* () {
            const conn = (0, pool_1.pool)(options.uri, mysqlOptions);
            return (sql) => __awaiter(this, void 0, void 0, function* () {
                const data = yield conn.query(sql);
                return data[0];
            });
        });
        this.opts = Object.assign({ table: 'keyv', keySize: 255 }, options);
        const createTable = `CREATE TABLE IF NOT EXISTS ${this.opts.table}(id VARCHAR(${Number(this.opts.keySize)}) PRIMARY KEY, value TEXT )`;
        const connected = connection().then((query) => __awaiter(this, void 0, void 0, function* () {
            yield query(createTable);
            return query;
        })).catch(error => this.emit('error', error));
        this.query = (sqlString) => __awaiter(this, void 0, void 0, function* () {
            const query = yield connected;
            // @ts-expect-error - query is not a boolean
            return query(sqlString);
        });
    }
    get(key) {
        return __awaiter(this, void 0, void 0, function* () {
            const sql = `SELECT * FROM ${this.opts.table} WHERE id = ?`;
            const select = mysql2_1.default.format(sql, [key]);
            const rows = yield this.query(select);
            const row = rows[0];
            return row === null || row === void 0 ? void 0 : row.value;
        });
    }
    getMany(keys) {
        return __awaiter(this, void 0, void 0, function* () {
            const sql = `SELECT * FROM ${this.opts.table} WHERE id IN (?)`;
            const select = mysql2_1.default.format(sql, [keys]);
            const rows = yield this.query(select);
            const results = [];
            for (const key of keys) {
                const rowIndex = rows.findIndex((row) => row.id === key);
                results.push(rowIndex > -1 ? rows[rowIndex].value : undefined);
            }
            return results;
        });
    }
    set(key, value) {
        return __awaiter(this, void 0, void 0, function* () {
            const sql = `INSERT INTO ${this.opts.table} (id, value)
			VALUES(?, ?) 
			ON DUPLICATE KEY UPDATE value=?;`;
            const insert = [key, value, value];
            const upsert = mysql2_1.default.format(sql, insert);
            return this.query(upsert);
        });
    }
    delete(key) {
        return __awaiter(this, void 0, void 0, function* () {
            const sql = `SELECT * FROM ${this.opts.table} WHERE id = ?`;
            const select = mysql2_1.default.format(sql, [key]);
            const delSql = `DELETE FROM ${this.opts.table} WHERE id = ?`;
            const del = mysql2_1.default.format(delSql, [key]);
            const rows = yield this.query(select);
            const row = rows[0];
            if (row === undefined) {
                return false;
            }
            yield this.query(del);
            return true;
        });
    }
    deleteMany(key) {
        return __awaiter(this, void 0, void 0, function* () {
            const sql = `DELETE FROM ${this.opts.table} WHERE id IN (?)`;
            const del = mysql2_1.default.format(sql, [key]);
            const result = yield this.query(del);
            return result.affectedRows !== 0;
        });
    }
    clear() {
        return __awaiter(this, void 0, void 0, function* () {
            const sql = `DELETE FROM ${this.opts.table} WHERE id LIKE ?`;
            const del = mysql2_1.default.format(sql, [this.namespace ? `${this.namespace}:%` : '%']);
            yield this.query(del);
        });
    }
    iterator(namespace) {
        return __asyncGenerator(this, arguments, function* iterator_1() {
            const limit = Number.parseInt(this.opts.iterationLimit, 10) || 10;
            // @ts-expect-error - iterate
            function iterate(offset, options, query) {
                return __asyncGenerator(this, arguments, function* iterate_1() {
                    const sql = `SELECT * FROM ${options.table} WHERE id LIKE ? LIMIT ? OFFSET ?`;
                    const select = mysql2_1.default.format(sql, [`${namespace ? namespace + ':' : ''}%`, limit, offset]);
                    const entries = yield __await(query(select));
                    if (entries.length === 0) {
                        return yield __await(void 0);
                    }
                    for (const entry of entries) {
                        offset += 1;
                        yield yield __await([entry.id, entry.value]);
                    }
                    yield __await(yield* __asyncDelegator(__asyncValues(iterate(offset, options, query))));
                });
            }
            yield __await(yield* __asyncDelegator(__asyncValues(iterate(0, this.opts, this.query))));
        });
    }
    has(key) {
        return __awaiter(this, void 0, void 0, function* () {
            const exists = `SELECT EXISTS ( SELECT * FROM ${this.opts.table} WHERE id = '${key}' )`;
            const rows = yield this.query(exists);
            return Object.values(rows[0])[0] === 1;
        });
    }
    disconnect() {
        return __awaiter(this, void 0, void 0, function* () {
            (0, pool_1.endPool)();
        });
    }
}
module.exports = KeyvMysql;
//# sourceMappingURL=index.js.map