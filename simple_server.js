const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

const PORT = 8080;
const WEBSITE_DIR = './outpost-website-template-v1.1.1';

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.php': 'text/html'
};

const server = http.createServer((req, res) => {
    let filePath = path.join(WEBSITE_DIR, req.url === '/' ? 'index.php' : req.url);
    
    // Remove query parameters for file path
    const urlPath = req.url.split('?')[0];
    filePath = path.join(WEBSITE_DIR, urlPath === '/' ? 'index.php' : urlPath);
    
    // Security check - prevent directory traversal
    if (!filePath.startsWith(path.resolve(WEBSITE_DIR))) {
        res.writeHead(403);
        res.end('Forbidden');
        return;
    }
    
    const ext = path.extname(filePath);
    const contentType = mimeTypes[ext] || 'text/plain';
    
    // Check if file exists
    fs.access(filePath, fs.constants.F_OK, (err) => {
        if (err) {
            // Try with .php extension if no extension
            if (!ext && !filePath.endsWith('.php')) {
                filePath += '.php';
                return handleRequest();
            }
            
            res.writeHead(404);
            res.end(`
                <h1>404 - File Not Found</h1>
                <p>The requested file <code>${req.url}</code> was not found.</p>
                <h2>Available files:</h2>
                <ul>
                    <li><a href="/">index.php (Home)</a></li>
                    <li><a href="/compatibility.php">compatibility.php</a></li>
                    <li><a href="/admin.php">admin.php</a></li>
                    <li><a href="/link.php">link.php</a></li>
                </ul>
                <p><strong>Note:</strong> This is a simple file server. PHP functionality is limited.</p>
            `);
            return;
        }
        
        handleRequest();
    });
    
    function handleRequest() {
        if (ext === '.php') {
            // For PHP files, just serve the source code for now
            // In a real setup, you'd need PHP interpreter
            fs.readFile(filePath, 'utf8', (err, data) => {
                if (err) {
                    res.writeHead(500);
                    res.end('Server Error');
                    return;
                }
                
                res.writeHead(200, { 'Content-Type': 'text/html' });
                res.end(`
                    <h1>PHP File: ${path.basename(filePath)}</h1>
                    <p><strong>Note:</strong> This is showing the PHP source code. For full functionality, you need a PHP server.</p>
                    <hr>
                    <h2>File Contents:</h2>
                    <pre style="background: #f5f5f5; padding: 20px; overflow: auto;">${data.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                    <hr>
                    <h2>Quick Setup Instructions:</h2>
                    <ol>
                        <li>Install XAMPP from <a href="https://www.apachefriends.org/" target="_blank">apachefriends.org</a></li>
                        <li>Start Apache and MySQL in XAMPP Control Panel</li>
                        <li>Copy website files to C:\\xampp\\htdocs\\</li>
                        <li>Visit <a href="http://localhost/" target="_blank">http://localhost/</a></li>
                    </ol>
                `);
            });
        } else {
            // Serve static files normally
            fs.readFile(filePath, (err, data) => {
                if (err) {
                    res.writeHead(500);
                    res.end('Server Error');
                    return;
                }
                
                res.writeHead(200, { 'Content-Type': contentType });
                res.end(data);
            });
        }
    }
});

server.listen(PORT, () => {
    console.log(`🚀 Simple development server running at http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${path.resolve(WEBSITE_DIR)}`);
    console.log('');
    console.log('📋 Available pages:');
    console.log(`   🏠 Home: http://localhost:${PORT}/`);
    console.log(`   ⚙️  Compatibility: http://localhost:${PORT}/compatibility.php`);
    console.log(`   👤 Admin: http://localhost:${PORT}/admin.php`);
    console.log(`   🔗 Link: http://localhost:${PORT}/link.php`);
    console.log('');
    console.log('⚠️  Note: This serves PHP source code only. For full functionality:');
    console.log('   1. Install XAMPP');
    console.log('   2. Start Apache + MySQL');
    console.log('   3. Copy files to C:\\xampp\\htdocs\\');
    console.log('   4. Visit http://localhost/');
    console.log('');
    console.log('Press Ctrl+C to stop the server');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Server shutting down...');
    server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
    });
});
